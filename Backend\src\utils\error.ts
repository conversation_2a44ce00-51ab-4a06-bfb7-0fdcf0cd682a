class AppError extends Error {
  public statusCode: number;
  public status: string;
  public isOperational: boolean;

  constructor(message: string, statusCode: number) {
    super(message);

    // Set custom properties
    this.statusCode = statusCode;
    this.status = `${statusCode}`.startsWith('4') ? 'fail' : 'error';
    this.isOperational = true;
    this.name = this.constructor.name;

    // Maintains proper stack trace (only in V8 engines like Node)
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, this.constructor);
    }
  }
}

class validationError extends AppError {
  constructor(message: string = "Validation failed") {
    super(message, 400);
  }
}

class notFoundError extends AppError {
  constructor(message: string = "Not found") {
    super(message, 404);
  }
}

class unauthorizedError extends AppError {
  constructor(message: string = "Unauthorized") {
    super(message, 401);
  }
}

class forbiddenError extends AppError {
  constructor(message: string = "Forbidden") {
    super(message, 403);
  }
}

class internalServerError extends AppError {
  constructor(message: string = "Internal server error") {
    super(message, 500);
  }
}

export {
  AppError,
  validationError,
  notFoundError,
  unauthorizedError,
  forbiddenError,
  internalServerError,
};
