import { S3 } from "aws-sdk";
import validatedEnv from "./envSchema";

const {
  AWS_ACCESS_KEY_ID,
  AWS_SECRET_ACCESS_KEY,
  AWS_REGION,
  AWS_TEMP_BUCKET_NAME,
  AWS_PREM_BUCKET_NAME
} = validatedEnv;

// Initialize S3 client using validated env variables
const s3 = new S3({
  region: AWS_REGION,
  credentials: {
    accessKeyId: AWS_ACCESS_KEY_ID!,
    secretAccessKey: AWS_SECRET_ACCESS_KEY!,
  },
});

/**
 * Generate a pre-signed URL for uploading files to the temporary S3 bucket.
 * @param filename - Name of the file being uploaded
 * @param contentType - MIME type of the file (e.g. "image/png")
 * @returns A pre-signed S3 URL valid for 5 minutes
 */
async function generateUploadUrl(filename: string, contentType: string): Promise<string> {
  const params = {
    Bucket: AWS_TEMP_BUCKET_NAME, // using the temp bucket
    Key: `pending/${Date.now()}_${filename}`, // unique key
    Expires: 300, // 5 minutes
    ContentType: contentType,
  };

  try {
    const url = await s3.getSignedUrlPromise("putObject", params);
    return url;
  } catch (error) {
    console.error("❌ Error generating signed URL:", error);
    throw new Error("Failed to generate S3 upload URL");
  }
}

export default generateUploadUrl;
