import {Request} from '../lib/request';
import {Response} from '../lib/response';
import {AWSError} from '../lib/error';
import {Service} from '../lib/service';
import {WaiterConfiguration} from '../lib/service';
import {ServiceConfigurationOptions} from '../lib/service';
import {ConfigBase as Config} from '../lib/config-base';
interface Blob {}
declare class ECR extends Service {
  /**
   * Constructs a service object. This object has one method for each API operation.
   */
  constructor(options?: ECR.Types.ClientConfiguration)
  config: Config & ECR.Types.ClientConfiguration;
  /**
   * Checks the availability of one or more image layers in a repository. When an image is pushed to a repository, each image layer is checked to verify if it has been uploaded before. If it has been uploaded, then the image layer is skipped.  This operation is used by the Amazon ECR proxy and is not generally used by customers for pulling and pushing images. In most cases, you should use the docker CLI to pull, tag, and push images. 
   */
  batchCheckLayerAvailability(params: ECR.Types.BatchCheckLayerAvailabilityRequest, callback?: (err: AWSError, data: ECR.Types.BatchCheckLayerAvailabilityResponse) => void): Request<ECR.Types.BatchCheckLayerAvailabilityResponse, AWSError>;
  /**
   * Checks the availability of one or more image layers in a repository. When an image is pushed to a repository, each image layer is checked to verify if it has been uploaded before. If it has been uploaded, then the image layer is skipped.  This operation is used by the Amazon ECR proxy and is not generally used by customers for pulling and pushing images. In most cases, you should use the docker CLI to pull, tag, and push images. 
   */
  batchCheckLayerAvailability(callback?: (err: AWSError, data: ECR.Types.BatchCheckLayerAvailabilityResponse) => void): Request<ECR.Types.BatchCheckLayerAvailabilityResponse, AWSError>;
  /**
   * Deletes a list of specified images within a repository. Images are specified with either an imageTag or imageDigest. You can remove a tag from an image by specifying the image's tag in your request. When you remove the last tag from an image, the image is deleted from your repository. You can completely delete an image (and all of its tags) by specifying the image's digest in your request.
   */
  batchDeleteImage(params: ECR.Types.BatchDeleteImageRequest, callback?: (err: AWSError, data: ECR.Types.BatchDeleteImageResponse) => void): Request<ECR.Types.BatchDeleteImageResponse, AWSError>;
  /**
   * Deletes a list of specified images within a repository. Images are specified with either an imageTag or imageDigest. You can remove a tag from an image by specifying the image's tag in your request. When you remove the last tag from an image, the image is deleted from your repository. You can completely delete an image (and all of its tags) by specifying the image's digest in your request.
   */
  batchDeleteImage(callback?: (err: AWSError, data: ECR.Types.BatchDeleteImageResponse) => void): Request<ECR.Types.BatchDeleteImageResponse, AWSError>;
  /**
   * Gets detailed information for an image. Images are specified with either an imageTag or imageDigest. When an image is pulled, the BatchGetImage API is called once to retrieve the image manifest.
   */
  batchGetImage(params: ECR.Types.BatchGetImageRequest, callback?: (err: AWSError, data: ECR.Types.BatchGetImageResponse) => void): Request<ECR.Types.BatchGetImageResponse, AWSError>;
  /**
   * Gets detailed information for an image. Images are specified with either an imageTag or imageDigest. When an image is pulled, the BatchGetImage API is called once to retrieve the image manifest.
   */
  batchGetImage(callback?: (err: AWSError, data: ECR.Types.BatchGetImageResponse) => void): Request<ECR.Types.BatchGetImageResponse, AWSError>;
  /**
   * Gets the scanning configuration for one or more repositories.
   */
  batchGetRepositoryScanningConfiguration(params: ECR.Types.BatchGetRepositoryScanningConfigurationRequest, callback?: (err: AWSError, data: ECR.Types.BatchGetRepositoryScanningConfigurationResponse) => void): Request<ECR.Types.BatchGetRepositoryScanningConfigurationResponse, AWSError>;
  /**
   * Gets the scanning configuration for one or more repositories.
   */
  batchGetRepositoryScanningConfiguration(callback?: (err: AWSError, data: ECR.Types.BatchGetRepositoryScanningConfigurationResponse) => void): Request<ECR.Types.BatchGetRepositoryScanningConfigurationResponse, AWSError>;
  /**
   * Informs Amazon ECR that the image layer upload has completed for a specified registry, repository name, and upload ID. You can optionally provide a sha256 digest of the image layer for data validation purposes. When an image is pushed, the CompleteLayerUpload API is called once per each new image layer to verify that the upload has completed.  This operation is used by the Amazon ECR proxy and is not generally used by customers for pulling and pushing images. In most cases, you should use the docker CLI to pull, tag, and push images. 
   */
  completeLayerUpload(params: ECR.Types.CompleteLayerUploadRequest, callback?: (err: AWSError, data: ECR.Types.CompleteLayerUploadResponse) => void): Request<ECR.Types.CompleteLayerUploadResponse, AWSError>;
  /**
   * Informs Amazon ECR that the image layer upload has completed for a specified registry, repository name, and upload ID. You can optionally provide a sha256 digest of the image layer for data validation purposes. When an image is pushed, the CompleteLayerUpload API is called once per each new image layer to verify that the upload has completed.  This operation is used by the Amazon ECR proxy and is not generally used by customers for pulling and pushing images. In most cases, you should use the docker CLI to pull, tag, and push images. 
   */
  completeLayerUpload(callback?: (err: AWSError, data: ECR.Types.CompleteLayerUploadResponse) => void): Request<ECR.Types.CompleteLayerUploadResponse, AWSError>;
  /**
   * Creates a pull through cache rule. A pull through cache rule provides a way to cache images from an upstream registry source in your Amazon ECR private registry. For more information, see Using pull through cache rules in the Amazon Elastic Container Registry User Guide.
   */
  createPullThroughCacheRule(params: ECR.Types.CreatePullThroughCacheRuleRequest, callback?: (err: AWSError, data: ECR.Types.CreatePullThroughCacheRuleResponse) => void): Request<ECR.Types.CreatePullThroughCacheRuleResponse, AWSError>;
  /**
   * Creates a pull through cache rule. A pull through cache rule provides a way to cache images from an upstream registry source in your Amazon ECR private registry. For more information, see Using pull through cache rules in the Amazon Elastic Container Registry User Guide.
   */
  createPullThroughCacheRule(callback?: (err: AWSError, data: ECR.Types.CreatePullThroughCacheRuleResponse) => void): Request<ECR.Types.CreatePullThroughCacheRuleResponse, AWSError>;
  /**
   * Creates a repository. For more information, see Amazon ECR repositories in the Amazon Elastic Container Registry User Guide.
   */
  createRepository(params: ECR.Types.CreateRepositoryRequest, callback?: (err: AWSError, data: ECR.Types.CreateRepositoryResponse) => void): Request<ECR.Types.CreateRepositoryResponse, AWSError>;
  /**
   * Creates a repository. For more information, see Amazon ECR repositories in the Amazon Elastic Container Registry User Guide.
   */
  createRepository(callback?: (err: AWSError, data: ECR.Types.CreateRepositoryResponse) => void): Request<ECR.Types.CreateRepositoryResponse, AWSError>;
  /**
   * Creates a repository creation template. This template is used to define the settings for repositories created by Amazon ECR on your behalf. For example, repositories created through pull through cache actions. For more information, see Private repository creation templates in the Amazon Elastic Container Registry User Guide.
   */
  createRepositoryCreationTemplate(params: ECR.Types.CreateRepositoryCreationTemplateRequest, callback?: (err: AWSError, data: ECR.Types.CreateRepositoryCreationTemplateResponse) => void): Request<ECR.Types.CreateRepositoryCreationTemplateResponse, AWSError>;
  /**
   * Creates a repository creation template. This template is used to define the settings for repositories created by Amazon ECR on your behalf. For example, repositories created through pull through cache actions. For more information, see Private repository creation templates in the Amazon Elastic Container Registry User Guide.
   */
  createRepositoryCreationTemplate(callback?: (err: AWSError, data: ECR.Types.CreateRepositoryCreationTemplateResponse) => void): Request<ECR.Types.CreateRepositoryCreationTemplateResponse, AWSError>;
  /**
   * Deletes the lifecycle policy associated with the specified repository.
   */
  deleteLifecyclePolicy(params: ECR.Types.DeleteLifecyclePolicyRequest, callback?: (err: AWSError, data: ECR.Types.DeleteLifecyclePolicyResponse) => void): Request<ECR.Types.DeleteLifecyclePolicyResponse, AWSError>;
  /**
   * Deletes the lifecycle policy associated with the specified repository.
   */
  deleteLifecyclePolicy(callback?: (err: AWSError, data: ECR.Types.DeleteLifecyclePolicyResponse) => void): Request<ECR.Types.DeleteLifecyclePolicyResponse, AWSError>;
  /**
   * Deletes a pull through cache rule.
   */
  deletePullThroughCacheRule(params: ECR.Types.DeletePullThroughCacheRuleRequest, callback?: (err: AWSError, data: ECR.Types.DeletePullThroughCacheRuleResponse) => void): Request<ECR.Types.DeletePullThroughCacheRuleResponse, AWSError>;
  /**
   * Deletes a pull through cache rule.
   */
  deletePullThroughCacheRule(callback?: (err: AWSError, data: ECR.Types.DeletePullThroughCacheRuleResponse) => void): Request<ECR.Types.DeletePullThroughCacheRuleResponse, AWSError>;
  /**
   * Deletes the registry permissions policy.
   */
  deleteRegistryPolicy(params: ECR.Types.DeleteRegistryPolicyRequest, callback?: (err: AWSError, data: ECR.Types.DeleteRegistryPolicyResponse) => void): Request<ECR.Types.DeleteRegistryPolicyResponse, AWSError>;
  /**
   * Deletes the registry permissions policy.
   */
  deleteRegistryPolicy(callback?: (err: AWSError, data: ECR.Types.DeleteRegistryPolicyResponse) => void): Request<ECR.Types.DeleteRegistryPolicyResponse, AWSError>;
  /**
   * Deletes a repository. If the repository isn't empty, you must either delete the contents of the repository or use the force option to delete the repository and have Amazon ECR delete all of its contents on your behalf.
   */
  deleteRepository(params: ECR.Types.DeleteRepositoryRequest, callback?: (err: AWSError, data: ECR.Types.DeleteRepositoryResponse) => void): Request<ECR.Types.DeleteRepositoryResponse, AWSError>;
  /**
   * Deletes a repository. If the repository isn't empty, you must either delete the contents of the repository or use the force option to delete the repository and have Amazon ECR delete all of its contents on your behalf.
   */
  deleteRepository(callback?: (err: AWSError, data: ECR.Types.DeleteRepositoryResponse) => void): Request<ECR.Types.DeleteRepositoryResponse, AWSError>;
  /**
   * Deletes a repository creation template.
   */
  deleteRepositoryCreationTemplate(params: ECR.Types.DeleteRepositoryCreationTemplateRequest, callback?: (err: AWSError, data: ECR.Types.DeleteRepositoryCreationTemplateResponse) => void): Request<ECR.Types.DeleteRepositoryCreationTemplateResponse, AWSError>;
  /**
   * Deletes a repository creation template.
   */
  deleteRepositoryCreationTemplate(callback?: (err: AWSError, data: ECR.Types.DeleteRepositoryCreationTemplateResponse) => void): Request<ECR.Types.DeleteRepositoryCreationTemplateResponse, AWSError>;
  /**
   * Deletes the repository policy associated with the specified repository.
   */
  deleteRepositoryPolicy(params: ECR.Types.DeleteRepositoryPolicyRequest, callback?: (err: AWSError, data: ECR.Types.DeleteRepositoryPolicyResponse) => void): Request<ECR.Types.DeleteRepositoryPolicyResponse, AWSError>;
  /**
   * Deletes the repository policy associated with the specified repository.
   */
  deleteRepositoryPolicy(callback?: (err: AWSError, data: ECR.Types.DeleteRepositoryPolicyResponse) => void): Request<ECR.Types.DeleteRepositoryPolicyResponse, AWSError>;
  /**
   * Returns the replication status for a specified image.
   */
  describeImageReplicationStatus(params: ECR.Types.DescribeImageReplicationStatusRequest, callback?: (err: AWSError, data: ECR.Types.DescribeImageReplicationStatusResponse) => void): Request<ECR.Types.DescribeImageReplicationStatusResponse, AWSError>;
  /**
   * Returns the replication status for a specified image.
   */
  describeImageReplicationStatus(callback?: (err: AWSError, data: ECR.Types.DescribeImageReplicationStatusResponse) => void): Request<ECR.Types.DescribeImageReplicationStatusResponse, AWSError>;
  /**
   * Returns the scan findings for the specified image.
   */
  describeImageScanFindings(params: ECR.Types.DescribeImageScanFindingsRequest, callback?: (err: AWSError, data: ECR.Types.DescribeImageScanFindingsResponse) => void): Request<ECR.Types.DescribeImageScanFindingsResponse, AWSError>;
  /**
   * Returns the scan findings for the specified image.
   */
  describeImageScanFindings(callback?: (err: AWSError, data: ECR.Types.DescribeImageScanFindingsResponse) => void): Request<ECR.Types.DescribeImageScanFindingsResponse, AWSError>;
  /**
   * Returns metadata about the images in a repository.  Beginning with Docker version 1.9, the Docker client compresses image layers before pushing them to a V2 Docker registry. The output of the docker images command shows the uncompressed image size, so it may return a larger image size than the image sizes returned by DescribeImages. 
   */
  describeImages(params: ECR.Types.DescribeImagesRequest, callback?: (err: AWSError, data: ECR.Types.DescribeImagesResponse) => void): Request<ECR.Types.DescribeImagesResponse, AWSError>;
  /**
   * Returns metadata about the images in a repository.  Beginning with Docker version 1.9, the Docker client compresses image layers before pushing them to a V2 Docker registry. The output of the docker images command shows the uncompressed image size, so it may return a larger image size than the image sizes returned by DescribeImages. 
   */
  describeImages(callback?: (err: AWSError, data: ECR.Types.DescribeImagesResponse) => void): Request<ECR.Types.DescribeImagesResponse, AWSError>;
  /**
   * Returns the pull through cache rules for a registry.
   */
  describePullThroughCacheRules(params: ECR.Types.DescribePullThroughCacheRulesRequest, callback?: (err: AWSError, data: ECR.Types.DescribePullThroughCacheRulesResponse) => void): Request<ECR.Types.DescribePullThroughCacheRulesResponse, AWSError>;
  /**
   * Returns the pull through cache rules for a registry.
   */
  describePullThroughCacheRules(callback?: (err: AWSError, data: ECR.Types.DescribePullThroughCacheRulesResponse) => void): Request<ECR.Types.DescribePullThroughCacheRulesResponse, AWSError>;
  /**
   * Describes the settings for a registry. The replication configuration for a repository can be created or updated with the PutReplicationConfiguration API action.
   */
  describeRegistry(params: ECR.Types.DescribeRegistryRequest, callback?: (err: AWSError, data: ECR.Types.DescribeRegistryResponse) => void): Request<ECR.Types.DescribeRegistryResponse, AWSError>;
  /**
   * Describes the settings for a registry. The replication configuration for a repository can be created or updated with the PutReplicationConfiguration API action.
   */
  describeRegistry(callback?: (err: AWSError, data: ECR.Types.DescribeRegistryResponse) => void): Request<ECR.Types.DescribeRegistryResponse, AWSError>;
  /**
   * Describes image repositories in a registry.
   */
  describeRepositories(params: ECR.Types.DescribeRepositoriesRequest, callback?: (err: AWSError, data: ECR.Types.DescribeRepositoriesResponse) => void): Request<ECR.Types.DescribeRepositoriesResponse, AWSError>;
  /**
   * Describes image repositories in a registry.
   */
  describeRepositories(callback?: (err: AWSError, data: ECR.Types.DescribeRepositoriesResponse) => void): Request<ECR.Types.DescribeRepositoriesResponse, AWSError>;
  /**
   * Returns details about the repository creation templates in a registry. The prefixes request parameter can be used to return the details for a specific repository creation template.
   */
  describeRepositoryCreationTemplates(params: ECR.Types.DescribeRepositoryCreationTemplatesRequest, callback?: (err: AWSError, data: ECR.Types.DescribeRepositoryCreationTemplatesResponse) => void): Request<ECR.Types.DescribeRepositoryCreationTemplatesResponse, AWSError>;
  /**
   * Returns details about the repository creation templates in a registry. The prefixes request parameter can be used to return the details for a specific repository creation template.
   */
  describeRepositoryCreationTemplates(callback?: (err: AWSError, data: ECR.Types.DescribeRepositoryCreationTemplatesResponse) => void): Request<ECR.Types.DescribeRepositoryCreationTemplatesResponse, AWSError>;
  /**
   * Retrieves the basic scan type version name.
   */
  getAccountSetting(params: ECR.Types.GetAccountSettingRequest, callback?: (err: AWSError, data: ECR.Types.GetAccountSettingResponse) => void): Request<ECR.Types.GetAccountSettingResponse, AWSError>;
  /**
   * Retrieves the basic scan type version name.
   */
  getAccountSetting(callback?: (err: AWSError, data: ECR.Types.GetAccountSettingResponse) => void): Request<ECR.Types.GetAccountSettingResponse, AWSError>;
  /**
   * Retrieves an authorization token. An authorization token represents your IAM authentication credentials and can be used to access any Amazon ECR registry that your IAM principal has access to. The authorization token is valid for 12 hours. The authorizationToken returned is a base64 encoded string that can be decoded and used in a docker login command to authenticate to a registry. The CLI offers an get-login-password command that simplifies the login process. For more information, see Registry authentication in the Amazon Elastic Container Registry User Guide.
   */
  getAuthorizationToken(params: ECR.Types.GetAuthorizationTokenRequest, callback?: (err: AWSError, data: ECR.Types.GetAuthorizationTokenResponse) => void): Request<ECR.Types.GetAuthorizationTokenResponse, AWSError>;
  /**
   * Retrieves an authorization token. An authorization token represents your IAM authentication credentials and can be used to access any Amazon ECR registry that your IAM principal has access to. The authorization token is valid for 12 hours. The authorizationToken returned is a base64 encoded string that can be decoded and used in a docker login command to authenticate to a registry. The CLI offers an get-login-password command that simplifies the login process. For more information, see Registry authentication in the Amazon Elastic Container Registry User Guide.
   */
  getAuthorizationToken(callback?: (err: AWSError, data: ECR.Types.GetAuthorizationTokenResponse) => void): Request<ECR.Types.GetAuthorizationTokenResponse, AWSError>;
  /**
   * Retrieves the pre-signed Amazon S3 download URL corresponding to an image layer. You can only get URLs for image layers that are referenced in an image. When an image is pulled, the GetDownloadUrlForLayer API is called once per image layer that is not already cached.  This operation is used by the Amazon ECR proxy and is not generally used by customers for pulling and pushing images. In most cases, you should use the docker CLI to pull, tag, and push images. 
   */
  getDownloadUrlForLayer(params: ECR.Types.GetDownloadUrlForLayerRequest, callback?: (err: AWSError, data: ECR.Types.GetDownloadUrlForLayerResponse) => void): Request<ECR.Types.GetDownloadUrlForLayerResponse, AWSError>;
  /**
   * Retrieves the pre-signed Amazon S3 download URL corresponding to an image layer. You can only get URLs for image layers that are referenced in an image. When an image is pulled, the GetDownloadUrlForLayer API is called once per image layer that is not already cached.  This operation is used by the Amazon ECR proxy and is not generally used by customers for pulling and pushing images. In most cases, you should use the docker CLI to pull, tag, and push images. 
   */
  getDownloadUrlForLayer(callback?: (err: AWSError, data: ECR.Types.GetDownloadUrlForLayerResponse) => void): Request<ECR.Types.GetDownloadUrlForLayerResponse, AWSError>;
  /**
   * Retrieves the lifecycle policy for the specified repository.
   */
  getLifecyclePolicy(params: ECR.Types.GetLifecyclePolicyRequest, callback?: (err: AWSError, data: ECR.Types.GetLifecyclePolicyResponse) => void): Request<ECR.Types.GetLifecyclePolicyResponse, AWSError>;
  /**
   * Retrieves the lifecycle policy for the specified repository.
   */
  getLifecyclePolicy(callback?: (err: AWSError, data: ECR.Types.GetLifecyclePolicyResponse) => void): Request<ECR.Types.GetLifecyclePolicyResponse, AWSError>;
  /**
   * Retrieves the results of the lifecycle policy preview request for the specified repository.
   */
  getLifecyclePolicyPreview(params: ECR.Types.GetLifecyclePolicyPreviewRequest, callback?: (err: AWSError, data: ECR.Types.GetLifecyclePolicyPreviewResponse) => void): Request<ECR.Types.GetLifecyclePolicyPreviewResponse, AWSError>;
  /**
   * Retrieves the results of the lifecycle policy preview request for the specified repository.
   */
  getLifecyclePolicyPreview(callback?: (err: AWSError, data: ECR.Types.GetLifecyclePolicyPreviewResponse) => void): Request<ECR.Types.GetLifecyclePolicyPreviewResponse, AWSError>;
  /**
   * Retrieves the permissions policy for a registry.
   */
  getRegistryPolicy(params: ECR.Types.GetRegistryPolicyRequest, callback?: (err: AWSError, data: ECR.Types.GetRegistryPolicyResponse) => void): Request<ECR.Types.GetRegistryPolicyResponse, AWSError>;
  /**
   * Retrieves the permissions policy for a registry.
   */
  getRegistryPolicy(callback?: (err: AWSError, data: ECR.Types.GetRegistryPolicyResponse) => void): Request<ECR.Types.GetRegistryPolicyResponse, AWSError>;
  /**
   * Retrieves the scanning configuration for a registry.
   */
  getRegistryScanningConfiguration(params: ECR.Types.GetRegistryScanningConfigurationRequest, callback?: (err: AWSError, data: ECR.Types.GetRegistryScanningConfigurationResponse) => void): Request<ECR.Types.GetRegistryScanningConfigurationResponse, AWSError>;
  /**
   * Retrieves the scanning configuration for a registry.
   */
  getRegistryScanningConfiguration(callback?: (err: AWSError, data: ECR.Types.GetRegistryScanningConfigurationResponse) => void): Request<ECR.Types.GetRegistryScanningConfigurationResponse, AWSError>;
  /**
   * Retrieves the repository policy for the specified repository.
   */
  getRepositoryPolicy(params: ECR.Types.GetRepositoryPolicyRequest, callback?: (err: AWSError, data: ECR.Types.GetRepositoryPolicyResponse) => void): Request<ECR.Types.GetRepositoryPolicyResponse, AWSError>;
  /**
   * Retrieves the repository policy for the specified repository.
   */
  getRepositoryPolicy(callback?: (err: AWSError, data: ECR.Types.GetRepositoryPolicyResponse) => void): Request<ECR.Types.GetRepositoryPolicyResponse, AWSError>;
  /**
   * Notifies Amazon ECR that you intend to upload an image layer. When an image is pushed, the InitiateLayerUpload API is called once per image layer that has not already been uploaded. Whether or not an image layer has been uploaded is determined by the BatchCheckLayerAvailability API action.  This operation is used by the Amazon ECR proxy and is not generally used by customers for pulling and pushing images. In most cases, you should use the docker CLI to pull, tag, and push images. 
   */
  initiateLayerUpload(params: ECR.Types.InitiateLayerUploadRequest, callback?: (err: AWSError, data: ECR.Types.InitiateLayerUploadResponse) => void): Request<ECR.Types.InitiateLayerUploadResponse, AWSError>;
  /**
   * Notifies Amazon ECR that you intend to upload an image layer. When an image is pushed, the InitiateLayerUpload API is called once per image layer that has not already been uploaded. Whether or not an image layer has been uploaded is determined by the BatchCheckLayerAvailability API action.  This operation is used by the Amazon ECR proxy and is not generally used by customers for pulling and pushing images. In most cases, you should use the docker CLI to pull, tag, and push images. 
   */
  initiateLayerUpload(callback?: (err: AWSError, data: ECR.Types.InitiateLayerUploadResponse) => void): Request<ECR.Types.InitiateLayerUploadResponse, AWSError>;
  /**
   * Lists all the image IDs for the specified repository. You can filter images based on whether or not they are tagged by using the tagStatus filter and specifying either TAGGED, UNTAGGED or ANY. For example, you can filter your results to return only UNTAGGED images and then pipe that result to a BatchDeleteImage operation to delete them. Or, you can filter your results to return only TAGGED images to list all of the tags in your repository.
   */
  listImages(params: ECR.Types.ListImagesRequest, callback?: (err: AWSError, data: ECR.Types.ListImagesResponse) => void): Request<ECR.Types.ListImagesResponse, AWSError>;
  /**
   * Lists all the image IDs for the specified repository. You can filter images based on whether or not they are tagged by using the tagStatus filter and specifying either TAGGED, UNTAGGED or ANY. For example, you can filter your results to return only UNTAGGED images and then pipe that result to a BatchDeleteImage operation to delete them. Or, you can filter your results to return only TAGGED images to list all of the tags in your repository.
   */
  listImages(callback?: (err: AWSError, data: ECR.Types.ListImagesResponse) => void): Request<ECR.Types.ListImagesResponse, AWSError>;
  /**
   * List the tags for an Amazon ECR resource.
   */
  listTagsForResource(params: ECR.Types.ListTagsForResourceRequest, callback?: (err: AWSError, data: ECR.Types.ListTagsForResourceResponse) => void): Request<ECR.Types.ListTagsForResourceResponse, AWSError>;
  /**
   * List the tags for an Amazon ECR resource.
   */
  listTagsForResource(callback?: (err: AWSError, data: ECR.Types.ListTagsForResourceResponse) => void): Request<ECR.Types.ListTagsForResourceResponse, AWSError>;
  /**
   * Allows you to change the basic scan type version by setting the name parameter to either CLAIR to AWS_NATIVE.
   */
  putAccountSetting(params: ECR.Types.PutAccountSettingRequest, callback?: (err: AWSError, data: ECR.Types.PutAccountSettingResponse) => void): Request<ECR.Types.PutAccountSettingResponse, AWSError>;
  /**
   * Allows you to change the basic scan type version by setting the name parameter to either CLAIR to AWS_NATIVE.
   */
  putAccountSetting(callback?: (err: AWSError, data: ECR.Types.PutAccountSettingResponse) => void): Request<ECR.Types.PutAccountSettingResponse, AWSError>;
  /**
   * Creates or updates the image manifest and tags associated with an image. When an image is pushed and all new image layers have been uploaded, the PutImage API is called once to create or update the image manifest and the tags associated with the image.  This operation is used by the Amazon ECR proxy and is not generally used by customers for pulling and pushing images. In most cases, you should use the docker CLI to pull, tag, and push images. 
   */
  putImage(params: ECR.Types.PutImageRequest, callback?: (err: AWSError, data: ECR.Types.PutImageResponse) => void): Request<ECR.Types.PutImageResponse, AWSError>;
  /**
   * Creates or updates the image manifest and tags associated with an image. When an image is pushed and all new image layers have been uploaded, the PutImage API is called once to create or update the image manifest and the tags associated with the image.  This operation is used by the Amazon ECR proxy and is not generally used by customers for pulling and pushing images. In most cases, you should use the docker CLI to pull, tag, and push images. 
   */
  putImage(callback?: (err: AWSError, data: ECR.Types.PutImageResponse) => void): Request<ECR.Types.PutImageResponse, AWSError>;
  /**
   *  The PutImageScanningConfiguration API is being deprecated, in favor of specifying the image scanning configuration at the registry level. For more information, see PutRegistryScanningConfiguration.  Updates the image scanning configuration for the specified repository.
   */
  putImageScanningConfiguration(params: ECR.Types.PutImageScanningConfigurationRequest, callback?: (err: AWSError, data: ECR.Types.PutImageScanningConfigurationResponse) => void): Request<ECR.Types.PutImageScanningConfigurationResponse, AWSError>;
  /**
   *  The PutImageScanningConfiguration API is being deprecated, in favor of specifying the image scanning configuration at the registry level. For more information, see PutRegistryScanningConfiguration.  Updates the image scanning configuration for the specified repository.
   */
  putImageScanningConfiguration(callback?: (err: AWSError, data: ECR.Types.PutImageScanningConfigurationResponse) => void): Request<ECR.Types.PutImageScanningConfigurationResponse, AWSError>;
  /**
   * Updates the image tag mutability settings for the specified repository. For more information, see Image tag mutability in the Amazon Elastic Container Registry User Guide.
   */
  putImageTagMutability(params: ECR.Types.PutImageTagMutabilityRequest, callback?: (err: AWSError, data: ECR.Types.PutImageTagMutabilityResponse) => void): Request<ECR.Types.PutImageTagMutabilityResponse, AWSError>;
  /**
   * Updates the image tag mutability settings for the specified repository. For more information, see Image tag mutability in the Amazon Elastic Container Registry User Guide.
   */
  putImageTagMutability(callback?: (err: AWSError, data: ECR.Types.PutImageTagMutabilityResponse) => void): Request<ECR.Types.PutImageTagMutabilityResponse, AWSError>;
  /**
   * Creates or updates the lifecycle policy for the specified repository. For more information, see Lifecycle policy template.
   */
  putLifecyclePolicy(params: ECR.Types.PutLifecyclePolicyRequest, callback?: (err: AWSError, data: ECR.Types.PutLifecyclePolicyResponse) => void): Request<ECR.Types.PutLifecyclePolicyResponse, AWSError>;
  /**
   * Creates or updates the lifecycle policy for the specified repository. For more information, see Lifecycle policy template.
   */
  putLifecyclePolicy(callback?: (err: AWSError, data: ECR.Types.PutLifecyclePolicyResponse) => void): Request<ECR.Types.PutLifecyclePolicyResponse, AWSError>;
  /**
   * Creates or updates the permissions policy for your registry. A registry policy is used to specify permissions for another Amazon Web Services account and is used when configuring cross-account replication. For more information, see Registry permissions in the Amazon Elastic Container Registry User Guide.
   */
  putRegistryPolicy(params: ECR.Types.PutRegistryPolicyRequest, callback?: (err: AWSError, data: ECR.Types.PutRegistryPolicyResponse) => void): Request<ECR.Types.PutRegistryPolicyResponse, AWSError>;
  /**
   * Creates or updates the permissions policy for your registry. A registry policy is used to specify permissions for another Amazon Web Services account and is used when configuring cross-account replication. For more information, see Registry permissions in the Amazon Elastic Container Registry User Guide.
   */
  putRegistryPolicy(callback?: (err: AWSError, data: ECR.Types.PutRegistryPolicyResponse) => void): Request<ECR.Types.PutRegistryPolicyResponse, AWSError>;
  /**
   * Creates or updates the scanning configuration for your private registry.
   */
  putRegistryScanningConfiguration(params: ECR.Types.PutRegistryScanningConfigurationRequest, callback?: (err: AWSError, data: ECR.Types.PutRegistryScanningConfigurationResponse) => void): Request<ECR.Types.PutRegistryScanningConfigurationResponse, AWSError>;
  /**
   * Creates or updates the scanning configuration for your private registry.
   */
  putRegistryScanningConfiguration(callback?: (err: AWSError, data: ECR.Types.PutRegistryScanningConfigurationResponse) => void): Request<ECR.Types.PutRegistryScanningConfigurationResponse, AWSError>;
  /**
   * Creates or updates the replication configuration for a registry. The existing replication configuration for a repository can be retrieved with the DescribeRegistry API action. The first time the PutReplicationConfiguration API is called, a service-linked IAM role is created in your account for the replication process. For more information, see Using service-linked roles for Amazon ECR in the Amazon Elastic Container Registry User Guide. For more information on the custom role for replication, see Creating an IAM role for replication.  When configuring cross-account replication, the destination account must grant the source account permission to replicate. This permission is controlled using a registry permissions policy. For more information, see PutRegistryPolicy. 
   */
  putReplicationConfiguration(params: ECR.Types.PutReplicationConfigurationRequest, callback?: (err: AWSError, data: ECR.Types.PutReplicationConfigurationResponse) => void): Request<ECR.Types.PutReplicationConfigurationResponse, AWSError>;
  /**
   * Creates or updates the replication configuration for a registry. The existing replication configuration for a repository can be retrieved with the DescribeRegistry API action. The first time the PutReplicationConfiguration API is called, a service-linked IAM role is created in your account for the replication process. For more information, see Using service-linked roles for Amazon ECR in the Amazon Elastic Container Registry User Guide. For more information on the custom role for replication, see Creating an IAM role for replication.  When configuring cross-account replication, the destination account must grant the source account permission to replicate. This permission is controlled using a registry permissions policy. For more information, see PutRegistryPolicy. 
   */
  putReplicationConfiguration(callback?: (err: AWSError, data: ECR.Types.PutReplicationConfigurationResponse) => void): Request<ECR.Types.PutReplicationConfigurationResponse, AWSError>;
  /**
   * Applies a repository policy to the specified repository to control access permissions. For more information, see Amazon ECR Repository policies in the Amazon Elastic Container Registry User Guide.
   */
  setRepositoryPolicy(params: ECR.Types.SetRepositoryPolicyRequest, callback?: (err: AWSError, data: ECR.Types.SetRepositoryPolicyResponse) => void): Request<ECR.Types.SetRepositoryPolicyResponse, AWSError>;
  /**
   * Applies a repository policy to the specified repository to control access permissions. For more information, see Amazon ECR Repository policies in the Amazon Elastic Container Registry User Guide.
   */
  setRepositoryPolicy(callback?: (err: AWSError, data: ECR.Types.SetRepositoryPolicyResponse) => void): Request<ECR.Types.SetRepositoryPolicyResponse, AWSError>;
  /**
   * Starts an image vulnerability scan. An image scan can only be started once per 24 hours on an individual image. This limit includes if an image was scanned on initial push. For more information, see Image scanning in the Amazon Elastic Container Registry User Guide.
   */
  startImageScan(params: ECR.Types.StartImageScanRequest, callback?: (err: AWSError, data: ECR.Types.StartImageScanResponse) => void): Request<ECR.Types.StartImageScanResponse, AWSError>;
  /**
   * Starts an image vulnerability scan. An image scan can only be started once per 24 hours on an individual image. This limit includes if an image was scanned on initial push. For more information, see Image scanning in the Amazon Elastic Container Registry User Guide.
   */
  startImageScan(callback?: (err: AWSError, data: ECR.Types.StartImageScanResponse) => void): Request<ECR.Types.StartImageScanResponse, AWSError>;
  /**
   * Starts a preview of a lifecycle policy for the specified repository. This allows you to see the results before associating the lifecycle policy with the repository.
   */
  startLifecyclePolicyPreview(params: ECR.Types.StartLifecyclePolicyPreviewRequest, callback?: (err: AWSError, data: ECR.Types.StartLifecyclePolicyPreviewResponse) => void): Request<ECR.Types.StartLifecyclePolicyPreviewResponse, AWSError>;
  /**
   * Starts a preview of a lifecycle policy for the specified repository. This allows you to see the results before associating the lifecycle policy with the repository.
   */
  startLifecyclePolicyPreview(callback?: (err: AWSError, data: ECR.Types.StartLifecyclePolicyPreviewResponse) => void): Request<ECR.Types.StartLifecyclePolicyPreviewResponse, AWSError>;
  /**
   * Adds specified tags to a resource with the specified ARN. Existing tags on a resource are not changed if they are not specified in the request parameters.
   */
  tagResource(params: ECR.Types.TagResourceRequest, callback?: (err: AWSError, data: ECR.Types.TagResourceResponse) => void): Request<ECR.Types.TagResourceResponse, AWSError>;
  /**
   * Adds specified tags to a resource with the specified ARN. Existing tags on a resource are not changed if they are not specified in the request parameters.
   */
  tagResource(callback?: (err: AWSError, data: ECR.Types.TagResourceResponse) => void): Request<ECR.Types.TagResourceResponse, AWSError>;
  /**
   * Deletes specified tags from a resource.
   */
  untagResource(params: ECR.Types.UntagResourceRequest, callback?: (err: AWSError, data: ECR.Types.UntagResourceResponse) => void): Request<ECR.Types.UntagResourceResponse, AWSError>;
  /**
   * Deletes specified tags from a resource.
   */
  untagResource(callback?: (err: AWSError, data: ECR.Types.UntagResourceResponse) => void): Request<ECR.Types.UntagResourceResponse, AWSError>;
  /**
   * Updates an existing pull through cache rule.
   */
  updatePullThroughCacheRule(params: ECR.Types.UpdatePullThroughCacheRuleRequest, callback?: (err: AWSError, data: ECR.Types.UpdatePullThroughCacheRuleResponse) => void): Request<ECR.Types.UpdatePullThroughCacheRuleResponse, AWSError>;
  /**
   * Updates an existing pull through cache rule.
   */
  updatePullThroughCacheRule(callback?: (err: AWSError, data: ECR.Types.UpdatePullThroughCacheRuleResponse) => void): Request<ECR.Types.UpdatePullThroughCacheRuleResponse, AWSError>;
  /**
   * Updates an existing repository creation template.
   */
  updateRepositoryCreationTemplate(params: ECR.Types.UpdateRepositoryCreationTemplateRequest, callback?: (err: AWSError, data: ECR.Types.UpdateRepositoryCreationTemplateResponse) => void): Request<ECR.Types.UpdateRepositoryCreationTemplateResponse, AWSError>;
  /**
   * Updates an existing repository creation template.
   */
  updateRepositoryCreationTemplate(callback?: (err: AWSError, data: ECR.Types.UpdateRepositoryCreationTemplateResponse) => void): Request<ECR.Types.UpdateRepositoryCreationTemplateResponse, AWSError>;
  /**
   * Uploads an image layer part to Amazon ECR. When an image is pushed, each new image layer is uploaded in parts. The maximum size of each image layer part can be 20971520 bytes (or about 20MB). The UploadLayerPart API is called once per each new image layer part.  This operation is used by the Amazon ECR proxy and is not generally used by customers for pulling and pushing images. In most cases, you should use the docker CLI to pull, tag, and push images. 
   */
  uploadLayerPart(params: ECR.Types.UploadLayerPartRequest, callback?: (err: AWSError, data: ECR.Types.UploadLayerPartResponse) => void): Request<ECR.Types.UploadLayerPartResponse, AWSError>;
  /**
   * Uploads an image layer part to Amazon ECR. When an image is pushed, each new image layer is uploaded in parts. The maximum size of each image layer part can be 20971520 bytes (or about 20MB). The UploadLayerPart API is called once per each new image layer part.  This operation is used by the Amazon ECR proxy and is not generally used by customers for pulling and pushing images. In most cases, you should use the docker CLI to pull, tag, and push images. 
   */
  uploadLayerPart(callback?: (err: AWSError, data: ECR.Types.UploadLayerPartResponse) => void): Request<ECR.Types.UploadLayerPartResponse, AWSError>;
  /**
   * Validates an existing pull through cache rule for an upstream registry that requires authentication. This will retrieve the contents of the Amazon Web Services Secrets Manager secret, verify the syntax, and then validate that authentication to the upstream registry is successful.
   */
  validatePullThroughCacheRule(params: ECR.Types.ValidatePullThroughCacheRuleRequest, callback?: (err: AWSError, data: ECR.Types.ValidatePullThroughCacheRuleResponse) => void): Request<ECR.Types.ValidatePullThroughCacheRuleResponse, AWSError>;
  /**
   * Validates an existing pull through cache rule for an upstream registry that requires authentication. This will retrieve the contents of the Amazon Web Services Secrets Manager secret, verify the syntax, and then validate that authentication to the upstream registry is successful.
   */
  validatePullThroughCacheRule(callback?: (err: AWSError, data: ECR.Types.ValidatePullThroughCacheRuleResponse) => void): Request<ECR.Types.ValidatePullThroughCacheRuleResponse, AWSError>;
  /**
   * Waits for the imageScanComplete state by periodically calling the underlying ECR.describeImageScanFindingsoperation every 5 seconds (at most 60 times). Wait until an image scan is complete and findings can be accessed
   */
  waitFor(state: "imageScanComplete", params: ECR.Types.DescribeImageScanFindingsRequest & {$waiter?: WaiterConfiguration}, callback?: (err: AWSError, data: ECR.Types.DescribeImageScanFindingsResponse) => void): Request<ECR.Types.DescribeImageScanFindingsResponse, AWSError>;
  /**
   * Waits for the imageScanComplete state by periodically calling the underlying ECR.describeImageScanFindingsoperation every 5 seconds (at most 60 times). Wait until an image scan is complete and findings can be accessed
   */
  waitFor(state: "imageScanComplete", callback?: (err: AWSError, data: ECR.Types.DescribeImageScanFindingsResponse) => void): Request<ECR.Types.DescribeImageScanFindingsResponse, AWSError>;
  /**
   * Waits for the lifecyclePolicyPreviewComplete state by periodically calling the underlying ECR.getLifecyclePolicyPreviewoperation every 5 seconds (at most 20 times). Wait until a lifecycle policy preview request is complete and results can be accessed
   */
  waitFor(state: "lifecyclePolicyPreviewComplete", params: ECR.Types.GetLifecyclePolicyPreviewRequest & {$waiter?: WaiterConfiguration}, callback?: (err: AWSError, data: ECR.Types.GetLifecyclePolicyPreviewResponse) => void): Request<ECR.Types.GetLifecyclePolicyPreviewResponse, AWSError>;
  /**
   * Waits for the lifecyclePolicyPreviewComplete state by periodically calling the underlying ECR.getLifecyclePolicyPreviewoperation every 5 seconds (at most 20 times). Wait until a lifecycle policy preview request is complete and results can be accessed
   */
  waitFor(state: "lifecyclePolicyPreviewComplete", callback?: (err: AWSError, data: ECR.Types.GetLifecyclePolicyPreviewResponse) => void): Request<ECR.Types.GetLifecyclePolicyPreviewResponse, AWSError>;
}
declare namespace ECR {
  export type AccountSettingName = string;
  export type AccountSettingValue = string;
  export type Arch = string;
  export type Arn = string;
  export interface Attribute {
    /**
     * The attribute key.
     */
    key: AttributeKey;
    /**
     * The value assigned to the attribute key.
     */
    value?: AttributeValue;
  }
  export type AttributeKey = string;
  export type AttributeList = Attribute[];
  export type AttributeValue = string;
  export type Author = string;
  export interface AuthorizationData {
    /**
     * A base64-encoded string that contains authorization data for the specified Amazon ECR registry. When the string is decoded, it is presented in the format user:password for private registry authentication using docker login.
     */
    authorizationToken?: Base64;
    /**
     * The Unix time in seconds and milliseconds when the authorization token expires. Authorization tokens are valid for 12 hours.
     */
    expiresAt?: ExpirationTimestamp;
    /**
     * The registry URL to use for this authorization token in a docker login command. The Amazon ECR registry URL format is https://aws_account_id.dkr.ecr.region.amazonaws.com. For example, https://************.dkr.ecr.us-east-1.amazonaws.com.. 
     */
    proxyEndpoint?: ProxyEndpoint;
  }
  export type AuthorizationDataList = AuthorizationData[];
  export interface AwsEcrContainerImageDetails {
    /**
     * The architecture of the Amazon ECR container image.
     */
    architecture?: Arch;
    /**
     * The image author of the Amazon ECR container image.
     */
    author?: Author;
    /**
     * The image hash of the Amazon ECR container image.
     */
    imageHash?: ImageDigest;
    /**
     * The image tags attached to the Amazon ECR container image.
     */
    imageTags?: ImageTagsList;
    /**
     * The platform of the Amazon ECR container image.
     */
    platform?: Platform;
    /**
     * The date and time the Amazon ECR container image was pushed.
     */
    pushedAt?: _Date;
    /**
     * The registry the Amazon ECR container image belongs to.
     */
    registry?: RegistryId;
    /**
     * The name of the repository the Amazon ECR container image resides in.
     */
    repositoryName?: RepositoryName;
  }
  export type Base64 = string;
  export type BaseScore = number;
  export interface BatchCheckLayerAvailabilityRequest {
    /**
     * The Amazon Web Services account ID associated with the registry that contains the image layers to check. If you do not specify a registry, the default registry is assumed.
     */
    registryId?: RegistryId;
    /**
     * The name of the repository that is associated with the image layers to check.
     */
    repositoryName: RepositoryName;
    /**
     * The digests of the image layers to check.
     */
    layerDigests: BatchedOperationLayerDigestList;
  }
  export interface BatchCheckLayerAvailabilityResponse {
    /**
     * A list of image layer objects corresponding to the image layer references in the request.
     */
    layers?: LayerList;
    /**
     * Any failures associated with the call.
     */
    failures?: LayerFailureList;
  }
  export interface BatchDeleteImageRequest {
    /**
     * The Amazon Web Services account ID associated with the registry that contains the image to delete. If you do not specify a registry, the default registry is assumed.
     */
    registryId?: RegistryId;
    /**
     * The repository that contains the image to delete.
     */
    repositoryName: RepositoryName;
    /**
     * A list of image ID references that correspond to images to delete. The format of the imageIds reference is imageTag=tag or imageDigest=digest.
     */
    imageIds: ImageIdentifierList;
  }
  export interface BatchDeleteImageResponse {
    /**
     * The image IDs of the deleted images.
     */
    imageIds?: ImageIdentifierList;
    /**
     * Any failures associated with the call.
     */
    failures?: ImageFailureList;
  }
  export interface BatchGetImageRequest {
    /**
     * The Amazon Web Services account ID associated with the registry that contains the images to describe. If you do not specify a registry, the default registry is assumed.
     */
    registryId?: RegistryId;
    /**
     * The repository that contains the images to describe.
     */
    repositoryName: RepositoryName;
    /**
     * A list of image ID references that correspond to images to describe. The format of the imageIds reference is imageTag=tag or imageDigest=digest.
     */
    imageIds: ImageIdentifierList;
    /**
     * The accepted media types for the request. Valid values: application/vnd.docker.distribution.manifest.v1+json | application/vnd.docker.distribution.manifest.v2+json | application/vnd.oci.image.manifest.v1+json 
     */
    acceptedMediaTypes?: MediaTypeList;
  }
  export interface BatchGetImageResponse {
    /**
     * A list of image objects corresponding to the image references in the request.
     */
    images?: ImageList;
    /**
     * Any failures associated with the call.
     */
    failures?: ImageFailureList;
  }
  export interface BatchGetRepositoryScanningConfigurationRequest {
    /**
     * One or more repository names to get the scanning configuration for.
     */
    repositoryNames: ScanningConfigurationRepositoryNameList;
  }
  export interface BatchGetRepositoryScanningConfigurationResponse {
    /**
     * The scanning configuration for the requested repositories.
     */
    scanningConfigurations?: RepositoryScanningConfigurationList;
    /**
     * Any failures associated with the call.
     */
    failures?: RepositoryScanningConfigurationFailureList;
  }
  export type BatchedOperationLayerDigest = string;
  export type BatchedOperationLayerDigestList = BatchedOperationLayerDigest[];
  export interface CompleteLayerUploadRequest {
    /**
     * The Amazon Web Services account ID associated with the registry to which to upload layers. If you do not specify a registry, the default registry is assumed.
     */
    registryId?: RegistryId;
    /**
     * The name of the repository to associate with the image layer.
     */
    repositoryName: RepositoryName;
    /**
     * The upload ID from a previous InitiateLayerUpload operation to associate with the image layer.
     */
    uploadId: UploadId;
    /**
     * The sha256 digest of the image layer.
     */
    layerDigests: LayerDigestList;
  }
  export interface CompleteLayerUploadResponse {
    /**
     * The registry ID associated with the request.
     */
    registryId?: RegistryId;
    /**
     * The repository name associated with the request.
     */
    repositoryName?: RepositoryName;
    /**
     * The upload ID associated with the layer.
     */
    uploadId?: UploadId;
    /**
     * The sha256 digest of the image layer.
     */
    layerDigest?: LayerDigest;
  }
  export interface CreatePullThroughCacheRuleRequest {
    /**
     * The repository name prefix to use when caching images from the source registry.
     */
    ecrRepositoryPrefix: PullThroughCacheRuleRepositoryPrefix;
    /**
     * The registry URL of the upstream public registry to use as the source for the pull through cache rule. The following is the syntax to use for each supported upstream registry.   Amazon ECR Public (ecr-public) - public.ecr.aws    Docker Hub (docker-hub) - registry-1.docker.io    Quay (quay) - quay.io    Kubernetes (k8s) - registry.k8s.io    GitHub Container Registry (github-container-registry) - ghcr.io    Microsoft Azure Container Registry (azure-container-registry) - &lt;custom&gt;.azurecr.io   
     */
    upstreamRegistryUrl: Url;
    /**
     * The Amazon Web Services account ID associated with the registry to create the pull through cache rule for. If you do not specify a registry, the default registry is assumed.
     */
    registryId?: RegistryId;
    /**
     * The name of the upstream registry.
     */
    upstreamRegistry?: UpstreamRegistry;
    /**
     * The Amazon Resource Name (ARN) of the Amazon Web Services Secrets Manager secret that identifies the credentials to authenticate to the upstream registry.
     */
    credentialArn?: CredentialArn;
  }
  export interface CreatePullThroughCacheRuleResponse {
    /**
     * The Amazon ECR repository prefix associated with the pull through cache rule.
     */
    ecrRepositoryPrefix?: PullThroughCacheRuleRepositoryPrefix;
    /**
     * The upstream registry URL associated with the pull through cache rule.
     */
    upstreamRegistryUrl?: Url;
    /**
     * The date and time, in JavaScript date format, when the pull through cache rule was created.
     */
    createdAt?: CreationTimestamp;
    /**
     * The registry ID associated with the request.
     */
    registryId?: RegistryId;
    /**
     * The name of the upstream registry associated with the pull through cache rule.
     */
    upstreamRegistry?: UpstreamRegistry;
    /**
     * The Amazon Resource Name (ARN) of the Amazon Web Services Secrets Manager secret associated with the pull through cache rule.
     */
    credentialArn?: CredentialArn;
  }
  export interface CreateRepositoryCreationTemplateRequest {
    /**
     * The repository namespace prefix to associate with the template. All repositories created using this namespace prefix will have the settings defined in this template applied. For example, a prefix of prod would apply to all repositories beginning with prod/. Similarly, a prefix of prod/team would apply to all repositories beginning with prod/team/. To apply a template to all repositories in your registry that don't have an associated creation template, you can use ROOT as the prefix.  There is always an assumed / applied to the end of the prefix. If you specify ecr-public as the prefix, Amazon ECR treats that as ecr-public/. When using a pull through cache rule, the repository prefix you specify during rule creation is what you should specify as your repository creation template prefix as well. 
     */
    prefix: Prefix;
    /**
     * A description for the repository creation template.
     */
    description?: RepositoryTemplateDescription;
    /**
     * The encryption configuration to use for repositories created using the template.
     */
    encryptionConfiguration?: EncryptionConfigurationForRepositoryCreationTemplate;
    /**
     * The metadata to apply to the repository to help you categorize and organize. Each tag consists of a key and an optional value, both of which you define. Tag keys can have a maximum character length of 128 characters, and tag values can have a maximum length of 256 characters.
     */
    resourceTags?: TagList;
    /**
     * The tag mutability setting for the repository. If this parameter is omitted, the default setting of MUTABLE will be used which will allow image tags to be overwritten. If IMMUTABLE is specified, all image tags within the repository will be immutable which will prevent them from being overwritten.
     */
    imageTagMutability?: ImageTagMutability;
    /**
     * The repository policy to apply to repositories created using the template. A repository policy is a permissions policy associated with a repository to control access permissions. 
     */
    repositoryPolicy?: RepositoryPolicyText;
    /**
     * The lifecycle policy to use for repositories created using the template.
     */
    lifecyclePolicy?: LifecyclePolicyTextForRepositoryCreationTemplate;
    /**
     * A list of enumerable strings representing the Amazon ECR repository creation scenarios that this template will apply towards. The two supported scenarios are PULL_THROUGH_CACHE and REPLICATION 
     */
    appliedFor: RCTAppliedForList;
    /**
     * The ARN of the role to be assumed by Amazon ECR. This role must be in the same account as the registry that you are configuring. Amazon ECR will assume your supplied role when the customRoleArn is specified. When this field isn't specified, Amazon ECR will use the service-linked role for the repository creation template.
     */
    customRoleArn?: CustomRoleArn;
  }
  export interface CreateRepositoryCreationTemplateResponse {
    /**
     * The registry ID associated with the request.
     */
    registryId?: RegistryId;
    /**
     * The details of the repository creation template associated with the request.
     */
    repositoryCreationTemplate?: RepositoryCreationTemplate;
  }
  export interface CreateRepositoryRequest {
    /**
     * The Amazon Web Services account ID associated with the registry to create the repository. If you do not specify a registry, the default registry is assumed.
     */
    registryId?: RegistryId;
    /**
     * The name to use for the repository. The repository name may be specified on its own (such as nginx-web-app) or it can be prepended with a namespace to group the repository into a category (such as project-a/nginx-web-app). The repository name must start with a letter and can only contain lowercase letters, numbers, hyphens, underscores, and forward slashes.
     */
    repositoryName: RepositoryName;
    /**
     * The metadata that you apply to the repository to help you categorize and organize them. Each tag consists of a key and an optional value, both of which you define. Tag keys can have a maximum character length of 128 characters, and tag values can have a maximum length of 256 characters.
     */
    tags?: TagList;
    /**
     * The tag mutability setting for the repository. If this parameter is omitted, the default setting of MUTABLE will be used which will allow image tags to be overwritten. If IMMUTABLE is specified, all image tags within the repository will be immutable which will prevent them from being overwritten.
     */
    imageTagMutability?: ImageTagMutability;
    /**
     * The image scanning configuration for the repository. This determines whether images are scanned for known vulnerabilities after being pushed to the repository.
     */
    imageScanningConfiguration?: ImageScanningConfiguration;
    /**
     * The encryption configuration for the repository. This determines how the contents of your repository are encrypted at rest.
     */
    encryptionConfiguration?: EncryptionConfiguration;
  }
  export interface CreateRepositoryResponse {
    /**
     * The repository that was created.
     */
    repository?: Repository;
  }
  export type CreationTimestamp = Date;
  export type CredentialArn = string;
  export type CustomRoleArn = string;
  export interface CvssScore {
    /**
     * The base CVSS score used for the finding.
     */
    baseScore?: BaseScore;
    /**
     * The vector string of the CVSS score.
     */
    scoringVector?: ScoringVector;
    /**
     * The source of the CVSS score.
     */
    source?: Source;
    /**
     * The version of CVSS used for the score.
     */
    version?: Version;
  }
  export interface CvssScoreAdjustment {
    /**
     * The metric used to adjust the CVSS score.
     */
    metric?: Metric;
    /**
     * The reason the CVSS score has been adjustment.
     */
    reason?: Reason;
  }
  export type CvssScoreAdjustmentList = CvssScoreAdjustment[];
  export interface CvssScoreDetails {
    /**
     * An object that contains details about adjustment Amazon Inspector made to the CVSS score.
     */
    adjustments?: CvssScoreAdjustmentList;
    /**
     * The CVSS score.
     */
    score?: Score;
    /**
     * The source for the CVSS score.
     */
    scoreSource?: Source;
    /**
     * The vector for the CVSS score.
     */
    scoringVector?: ScoringVector;
    /**
     * The CVSS version used in scoring.
     */
    version?: Version;
  }
  export type CvssScoreList = CvssScore[];
  export type _Date = Date;
  export interface DeleteLifecyclePolicyRequest {
    /**
     * The Amazon Web Services account ID associated with the registry that contains the repository. If you do not specify a registry, the default registry is assumed.
     */
    registryId?: RegistryId;
    /**
     * The name of the repository.
     */
    repositoryName: RepositoryName;
  }
  export interface DeleteLifecyclePolicyResponse {
    /**
     * The registry ID associated with the request.
     */
    registryId?: RegistryId;
    /**
     * The repository name associated with the request.
     */
    repositoryName?: RepositoryName;
    /**
     * The JSON lifecycle policy text.
     */
    lifecyclePolicyText?: LifecyclePolicyText;
    /**
     * The time stamp of the last time that the lifecycle policy was run.
     */
    lastEvaluatedAt?: EvaluationTimestamp;
  }
  export interface DeletePullThroughCacheRuleRequest {
    /**
     * The Amazon ECR repository prefix associated with the pull through cache rule to delete.
     */
    ecrRepositoryPrefix: PullThroughCacheRuleRepositoryPrefix;
    /**
     * The Amazon Web Services account ID associated with the registry that contains the pull through cache rule. If you do not specify a registry, the default registry is assumed.
     */
    registryId?: RegistryId;
  }
  export interface DeletePullThroughCacheRuleResponse {
    /**
     * The Amazon ECR repository prefix associated with the request.
     */
    ecrRepositoryPrefix?: PullThroughCacheRuleRepositoryPrefix;
    /**
     * The upstream registry URL associated with the pull through cache rule.
     */
    upstreamRegistryUrl?: Url;
    /**
     * The timestamp associated with the pull through cache rule.
     */
    createdAt?: CreationTimestamp;
    /**
     * The registry ID associated with the request.
     */
    registryId?: RegistryId;
    /**
     * The Amazon Resource Name (ARN) of the Amazon Web Services Secrets Manager secret associated with the pull through cache rule.
     */
    credentialArn?: CredentialArn;
  }
  export interface DeleteRegistryPolicyRequest {
  }
  export interface DeleteRegistryPolicyResponse {
    /**
     * The registry ID associated with the request.
     */
    registryId?: RegistryId;
    /**
     * The contents of the registry permissions policy that was deleted.
     */
    policyText?: RegistryPolicyText;
  }
  export interface DeleteRepositoryCreationTemplateRequest {
    /**
     * The repository namespace prefix associated with the repository creation template.
     */
    prefix: Prefix;
  }
  export interface DeleteRepositoryCreationTemplateResponse {
    /**
     * The registry ID associated with the request.
     */
    registryId?: RegistryId;
    /**
     * The details of the repository creation template that was deleted.
     */
    repositoryCreationTemplate?: RepositoryCreationTemplate;
  }
  export interface DeleteRepositoryPolicyRequest {
    /**
     * The Amazon Web Services account ID associated with the registry that contains the repository policy to delete. If you do not specify a registry, the default registry is assumed.
     */
    registryId?: RegistryId;
    /**
     * The name of the repository that is associated with the repository policy to delete.
     */
    repositoryName: RepositoryName;
  }
  export interface DeleteRepositoryPolicyResponse {
    /**
     * The registry ID associated with the request.
     */
    registryId?: RegistryId;
    /**
     * The repository name associated with the request.
     */
    repositoryName?: RepositoryName;
    /**
     * The JSON repository policy that was deleted from the repository.
     */
    policyText?: RepositoryPolicyText;
  }
  export interface DeleteRepositoryRequest {
    /**
     * The Amazon Web Services account ID associated with the registry that contains the repository to delete. If you do not specify a registry, the default registry is assumed.
     */
    registryId?: RegistryId;
    /**
     * The name of the repository to delete.
     */
    repositoryName: RepositoryName;
    /**
     * If true, deleting the repository force deletes the contents of the repository. If false, the repository must be empty before attempting to delete it.
     */
    force?: ForceFlag;
  }
  export interface DeleteRepositoryResponse {
    /**
     * The repository that was deleted.
     */
    repository?: Repository;
  }
  export interface DescribeImageReplicationStatusRequest {
    /**
     * The name of the repository that the image is in.
     */
    repositoryName: RepositoryName;
    imageId: ImageIdentifier;
    /**
     * The Amazon Web Services account ID associated with the registry. If you do not specify a registry, the default registry is assumed.
     */
    registryId?: RegistryId;
  }
  export interface DescribeImageReplicationStatusResponse {
    /**
     * The repository name associated with the request.
     */
    repositoryName?: RepositoryName;
    imageId?: ImageIdentifier;
    /**
     * The replication status details for the images in the specified repository.
     */
    replicationStatuses?: ImageReplicationStatusList;
  }
  export interface DescribeImageScanFindingsRequest {
    /**
     * The Amazon Web Services account ID associated with the registry that contains the repository in which to describe the image scan findings for. If you do not specify a registry, the default registry is assumed.
     */
    registryId?: RegistryId;
    /**
     * The repository for the image for which to describe the scan findings.
     */
    repositoryName: RepositoryName;
    imageId: ImageIdentifier;
    /**
     * The nextToken value returned from a previous paginated DescribeImageScanFindings request where maxResults was used and the results exceeded the value of that parameter. Pagination continues from the end of the previous results that returned the nextToken value. This value is null when there are no more results to return.
     */
    nextToken?: NextToken;
    /**
     * The maximum number of image scan results returned by DescribeImageScanFindings in paginated output. When this parameter is used, DescribeImageScanFindings only returns maxResults results in a single page along with a nextToken response element. The remaining results of the initial request can be seen by sending another DescribeImageScanFindings request with the returned nextToken value. This value can be between 1 and 1000. If this parameter is not used, then DescribeImageScanFindings returns up to 100 results and a nextToken value, if applicable.
     */
    maxResults?: MaxResults;
  }
  export interface DescribeImageScanFindingsResponse {
    /**
     * The registry ID associated with the request.
     */
    registryId?: RegistryId;
    /**
     * The repository name associated with the request.
     */
    repositoryName?: RepositoryName;
    imageId?: ImageIdentifier;
    /**
     * The current state of the scan.
     */
    imageScanStatus?: ImageScanStatus;
    /**
     * The information contained in the image scan findings.
     */
    imageScanFindings?: ImageScanFindings;
    /**
     * The nextToken value to include in a future DescribeImageScanFindings request. When the results of a DescribeImageScanFindings request exceed maxResults, this value can be used to retrieve the next page of results. This value is null when there are no more results to return.
     */
    nextToken?: NextToken;
  }
  export interface DescribeImagesFilter {
    /**
     * The tag status with which to filter your DescribeImages results. You can filter results based on whether they are TAGGED or UNTAGGED.
     */
    tagStatus?: TagStatus;
  }
  export interface DescribeImagesRequest {
    /**
     * The Amazon Web Services account ID associated with the registry that contains the repository in which to describe images. If you do not specify a registry, the default registry is assumed.
     */
    registryId?: RegistryId;
    /**
     * The repository that contains the images to describe.
     */
    repositoryName: RepositoryName;
    /**
     * The list of image IDs for the requested repository.
     */
    imageIds?: ImageIdentifierList;
    /**
     * The nextToken value returned from a previous paginated DescribeImages request where maxResults was used and the results exceeded the value of that parameter. Pagination continues from the end of the previous results that returned the nextToken value. This value is null when there are no more results to return. This option cannot be used when you specify images with imageIds.
     */
    nextToken?: NextToken;
    /**
     * The maximum number of repository results returned by DescribeImages in paginated output. When this parameter is used, DescribeImages only returns maxResults results in a single page along with a nextToken response element. The remaining results of the initial request can be seen by sending another DescribeImages request with the returned nextToken value. This value can be between 1 and 1000. If this parameter is not used, then DescribeImages returns up to 100 results and a nextToken value, if applicable. This option cannot be used when you specify images with imageIds.
     */
    maxResults?: MaxResults;
    /**
     * The filter key and value with which to filter your DescribeImages results.
     */
    filter?: DescribeImagesFilter;
  }
  export interface DescribeImagesResponse {
    /**
     * A list of ImageDetail objects that contain data about the image.
     */
    imageDetails?: ImageDetailList;
    /**
     * The nextToken value to include in a future DescribeImages request. When the results of a DescribeImages request exceed maxResults, this value can be used to retrieve the next page of results. This value is null when there are no more results to return.
     */
    nextToken?: NextToken;
  }
  export interface DescribePullThroughCacheRulesRequest {
    /**
     * The Amazon Web Services account ID associated with the registry to return the pull through cache rules for. If you do not specify a registry, the default registry is assumed.
     */
    registryId?: RegistryId;
    /**
     * The Amazon ECR repository prefixes associated with the pull through cache rules to return. If no repository prefix value is specified, all pull through cache rules are returned.
     */
    ecrRepositoryPrefixes?: PullThroughCacheRuleRepositoryPrefixList;
    /**
     * The nextToken value returned from a previous paginated DescribePullThroughCacheRulesRequest request where maxResults was used and the results exceeded the value of that parameter. Pagination continues from the end of the previous results that returned the nextToken value. This value is null when there are no more results to return.
     */
    nextToken?: NextToken;
    /**
     * The maximum number of pull through cache rules returned by DescribePullThroughCacheRulesRequest in paginated output. When this parameter is used, DescribePullThroughCacheRulesRequest only returns maxResults results in a single page along with a nextToken response element. The remaining results of the initial request can be seen by sending another DescribePullThroughCacheRulesRequest request with the returned nextToken value. This value can be between 1 and 1000. If this parameter is not used, then DescribePullThroughCacheRulesRequest returns up to 100 results and a nextToken value, if applicable.
     */
    maxResults?: MaxResults;
  }
  export interface DescribePullThroughCacheRulesResponse {
    /**
     * The details of the pull through cache rules.
     */
    pullThroughCacheRules?: PullThroughCacheRuleList;
    /**
     * The nextToken value to include in a future DescribePullThroughCacheRulesRequest request. When the results of a DescribePullThroughCacheRulesRequest request exceed maxResults, this value can be used to retrieve the next page of results. This value is null when there are no more results to return.
     */
    nextToken?: NextToken;
  }
  export interface DescribeRegistryRequest {
  }
  export interface DescribeRegistryResponse {
    /**
     * The registry ID associated with the request.
     */
    registryId?: RegistryId;
    /**
     * The replication configuration for the registry.
     */
    replicationConfiguration?: ReplicationConfiguration;
  }
  export interface DescribeRepositoriesRequest {
    /**
     * The Amazon Web Services account ID associated with the registry that contains the repositories to be described. If you do not specify a registry, the default registry is assumed.
     */
    registryId?: RegistryId;
    /**
     * A list of repositories to describe. If this parameter is omitted, then all repositories in a registry are described.
     */
    repositoryNames?: RepositoryNameList;
    /**
     * The nextToken value returned from a previous paginated DescribeRepositories request where maxResults was used and the results exceeded the value of that parameter. Pagination continues from the end of the previous results that returned the nextToken value. This value is null when there are no more results to return. This option cannot be used when you specify repositories with repositoryNames.  This token should be treated as an opaque identifier that is only used to retrieve the next items in a list and not for other programmatic purposes. 
     */
    nextToken?: NextToken;
    /**
     * The maximum number of repository results returned by DescribeRepositories in paginated output. When this parameter is used, DescribeRepositories only returns maxResults results in a single page along with a nextToken response element. The remaining results of the initial request can be seen by sending another DescribeRepositories request with the returned nextToken value. This value can be between 1 and 1000. If this parameter is not used, then DescribeRepositories returns up to 100 results and a nextToken value, if applicable. This option cannot be used when you specify repositories with repositoryNames.
     */
    maxResults?: MaxResults;
  }
  export interface DescribeRepositoriesResponse {
    /**
     * A list of repository objects corresponding to valid repositories.
     */
    repositories?: RepositoryList;
    /**
     * The nextToken value to include in a future DescribeRepositories request. When the results of a DescribeRepositories request exceed maxResults, this value can be used to retrieve the next page of results. This value is null when there are no more results to return.
     */
    nextToken?: NextToken;
  }
  export interface DescribeRepositoryCreationTemplatesRequest {
    /**
     * The repository namespace prefixes associated with the repository creation templates to describe. If this value is not specified, all repository creation templates are returned.
     */
    prefixes?: PrefixList;
    /**
     * The nextToken value returned from a previous paginated DescribeRepositoryCreationTemplates request where maxResults was used and the results exceeded the value of that parameter. Pagination continues from the end of the previous results that returned the nextToken value. This value is null when there are no more results to return.  This token should be treated as an opaque identifier that is only used to retrieve the next items in a list and not for other programmatic purposes. 
     */
    nextToken?: NextToken;
    /**
     * The maximum number of repository results returned by DescribeRepositoryCreationTemplatesRequest in paginated output. When this parameter is used, DescribeRepositoryCreationTemplatesRequest only returns maxResults results in a single page along with a nextToken response element. The remaining results of the initial request can be seen by sending another DescribeRepositoryCreationTemplatesRequest request with the returned nextToken value. This value can be between 1 and 1000. If this parameter is not used, then DescribeRepositoryCreationTemplatesRequest returns up to 100 results and a nextToken value, if applicable.
     */
    maxResults?: MaxResults;
  }
  export interface DescribeRepositoryCreationTemplatesResponse {
    /**
     * The registry ID associated with the request.
     */
    registryId?: RegistryId;
    /**
     * The details of the repository creation templates.
     */
    repositoryCreationTemplates?: RepositoryCreationTemplateList;
    /**
     * The nextToken value to include in a future DescribeRepositoryCreationTemplates request. When the results of a DescribeRepositoryCreationTemplates request exceed maxResults, this value can be used to retrieve the next page of results. This value is null when there are no more results to return.
     */
    nextToken?: NextToken;
  }
  export interface EncryptionConfiguration {
    /**
     * The encryption type to use. If you use the KMS encryption type, the contents of the repository will be encrypted using server-side encryption with Key Management Service key stored in KMS. When you use KMS to encrypt your data, you can either use the default Amazon Web Services managed KMS key for Amazon ECR, or specify your own KMS key, which you already created. For more information, see Protecting data using server-side encryption with an KMS key stored in Key Management Service (SSE-KMS) in the Amazon Simple Storage Service Console Developer Guide. If you use the AES256 encryption type, Amazon ECR uses server-side encryption with Amazon S3-managed encryption keys which encrypts the images in the repository using an AES256 encryption algorithm. For more information, see Protecting data using server-side encryption with Amazon S3-managed encryption keys (SSE-S3) in the Amazon Simple Storage Service Console Developer Guide.
     */
    encryptionType: EncryptionType;
    /**
     * If you use the KMS encryption type, specify the KMS key to use for encryption. The alias, key ID, or full ARN of the KMS key can be specified. The key must exist in the same Region as the repository. If no key is specified, the default Amazon Web Services managed KMS key for Amazon ECR will be used.
     */
    kmsKey?: KmsKey;
  }
  export interface EncryptionConfigurationForRepositoryCreationTemplate {
    /**
     * The encryption type to use. If you use the KMS encryption type, the contents of the repository will be encrypted using server-side encryption with Key Management Service key stored in KMS. When you use KMS to encrypt your data, you can either use the default Amazon Web Services managed KMS key for Amazon ECR, or specify your own KMS key, which you already created. For more information, see Protecting data using server-side encryption with an KMS key stored in Key Management Service (SSE-KMS) in the Amazon Simple Storage Service Console Developer Guide. If you use the AES256 encryption type, Amazon ECR uses server-side encryption with Amazon S3-managed encryption keys which encrypts the images in the repository using an AES256 encryption algorithm. For more information, see Protecting data using server-side encryption with Amazon S3-managed encryption keys (SSE-S3) in the Amazon Simple Storage Service Console Developer Guide.
     */
    encryptionType: EncryptionType;
    /**
     * If you use the KMS encryption type, specify the KMS key to use for encryption. The full ARN of the KMS key must be specified. The key must exist in the same Region as the repository. If no key is specified, the default Amazon Web Services managed KMS key for Amazon ECR will be used.
     */
    kmsKey?: KmsKeyForRepositoryCreationTemplate;
  }
  export type EncryptionType = "AES256"|"KMS"|string;
  export interface EnhancedImageScanFinding {
    /**
     * The Amazon Web Services account ID associated with the image.
     */
    awsAccountId?: RegistryId;
    /**
     * The description of the finding.
     */
    description?: FindingDescription;
    /**
     * The Amazon Resource Number (ARN) of the finding.
     */
    findingArn?: FindingArn;
    /**
     * The date and time that the finding was first observed.
     */
    firstObservedAt?: _Date;
    /**
     * The date and time that the finding was last observed.
     */
    lastObservedAt?: _Date;
    /**
     * An object that contains the details of a package vulnerability finding.
     */
    packageVulnerabilityDetails?: PackageVulnerabilityDetails;
    /**
     * An object that contains the details about how to remediate a finding.
     */
    remediation?: Remediation;
    /**
     * Contains information on the resources involved in a finding.
     */
    resources?: ResourceList;
    /**
     * The Amazon Inspector score given to the finding.
     */
    score?: Score;
    /**
     * An object that contains details of the Amazon Inspector score.
     */
    scoreDetails?: ScoreDetails;
    /**
     * The severity of the finding.
     */
    severity?: Severity;
    /**
     * The status of the finding.
     */
    status?: Status;
    /**
     * The title of the finding.
     */
    title?: Title;
    /**
     * The type of the finding.
     */
    type?: Type;
    /**
     * The date and time the finding was last updated at.
     */
    updatedAt?: _Date;
  }
  export type EnhancedImageScanFindingList = EnhancedImageScanFinding[];
  export type Epoch = number;
  export type EvaluationTimestamp = Date;
  export type ExpirationTimestamp = Date;
  export type FilePath = string;
  export type FindingArn = string;
  export type FindingDescription = string;
  export type FindingName = string;
  export type FindingSeverity = "INFORMATIONAL"|"LOW"|"MEDIUM"|"HIGH"|"CRITICAL"|"UNDEFINED"|string;
  export type FindingSeverityCounts = {[key: string]: SeverityCount};
  export type ForceFlag = boolean;
  export interface GetAccountSettingRequest {
    /**
     * Basic scan type version name. 
     */
    name: AccountSettingName;
  }
  export interface GetAccountSettingResponse {
    /**
     * Retrieves the basic scan type version name.
     */
    name?: AccountSettingName;
    /**
     * Retrieves the value that specifies what basic scan type is being used: AWS_NATIVE or CLAIR.
     */
    value?: AccountSettingName;
  }
  export type GetAuthorizationTokenRegistryIdList = RegistryId[];
  export interface GetAuthorizationTokenRequest {
    /**
     * A list of Amazon Web Services account IDs that are associated with the registries for which to get AuthorizationData objects. If you do not specify a registry, the default registry is assumed.
     */
    registryIds?: GetAuthorizationTokenRegistryIdList;
  }
  export interface GetAuthorizationTokenResponse {
    /**
     * A list of authorization token data objects that correspond to the registryIds values in the request.
     */
    authorizationData?: AuthorizationDataList;
  }
  export interface GetDownloadUrlForLayerRequest {
    /**
     * The Amazon Web Services account ID associated with the registry that contains the image layer to download. If you do not specify a registry, the default registry is assumed.
     */
    registryId?: RegistryId;
    /**
     * The name of the repository that is associated with the image layer to download.
     */
    repositoryName: RepositoryName;
    /**
     * The digest of the image layer to download.
     */
    layerDigest: LayerDigest;
  }
  export interface GetDownloadUrlForLayerResponse {
    /**
     * The pre-signed Amazon S3 download URL for the requested layer.
     */
    downloadUrl?: Url;
    /**
     * The digest of the image layer to download.
     */
    layerDigest?: LayerDigest;
  }
  export interface GetLifecyclePolicyPreviewRequest {
    /**
     * The Amazon Web Services account ID associated with the registry that contains the repository. If you do not specify a registry, the default registry is assumed.
     */
    registryId?: RegistryId;
    /**
     * The name of the repository.
     */
    repositoryName: RepositoryName;
    /**
     * The list of imageIDs to be included.
     */
    imageIds?: ImageIdentifierList;
    /**
     * The nextToken value returned from a previous paginated&#x2028; GetLifecyclePolicyPreviewRequest request where maxResults was used and the&#x2028; results exceeded the value of that parameter. Pagination continues from the end of the&#x2028; previous results that returned the nextToken value. This value is&#x2028; null when there are no more results to return. This option cannot be used when you specify images with imageIds.
     */
    nextToken?: NextToken;
    /**
     * The maximum number of repository results returned by GetLifecyclePolicyPreviewRequest in&#x2028; paginated output. When this parameter is used, GetLifecyclePolicyPreviewRequest only returns&#x2028; maxResults results in a single page along with a nextToken&#x2028; response element. The remaining results of the initial request can be seen by sending&#x2028; another GetLifecyclePolicyPreviewRequest request with the returned nextToken&#x2028; value. This value can be between 1 and 1000. If this&#x2028; parameter is not used, then GetLifecyclePolicyPreviewRequest returns up to&#x2028; 100 results and a nextToken value, if&#x2028; applicable. This option cannot be used when you specify images with imageIds.
     */
    maxResults?: LifecyclePreviewMaxResults;
    /**
     * An optional parameter that filters results based on image tag status and all tags, if tagged.
     */
    filter?: LifecyclePolicyPreviewFilter;
  }
  export interface GetLifecyclePolicyPreviewResponse {
    /**
     * The registry ID associated with the request.
     */
    registryId?: RegistryId;
    /**
     * The repository name associated with the request.
     */
    repositoryName?: RepositoryName;
    /**
     * The JSON lifecycle policy text.
     */
    lifecyclePolicyText?: LifecyclePolicyText;
    /**
     * The status of the lifecycle policy preview request.
     */
    status?: LifecyclePolicyPreviewStatus;
    /**
     * The nextToken value to include in a future GetLifecyclePolicyPreview request. When the results of a GetLifecyclePolicyPreview request exceed maxResults, this value can be used to retrieve the next page of results. This value is null when there are no more results to return.
     */
    nextToken?: NextToken;
    /**
     * The results of the lifecycle policy preview request.
     */
    previewResults?: LifecyclePolicyPreviewResultList;
    /**
     * The list of images that is returned as a result of the action.
     */
    summary?: LifecyclePolicyPreviewSummary;
  }
  export interface GetLifecyclePolicyRequest {
    /**
     * The Amazon Web Services account ID associated with the registry that contains the repository. If you do not specify a registry, the default registry is assumed.
     */
    registryId?: RegistryId;
    /**
     * The name of the repository.
     */
    repositoryName: RepositoryName;
  }
  export interface GetLifecyclePolicyResponse {
    /**
     * The registry ID associated with the request.
     */
    registryId?: RegistryId;
    /**
     * The repository name associated with the request.
     */
    repositoryName?: RepositoryName;
    /**
     * The JSON lifecycle policy text.
     */
    lifecyclePolicyText?: LifecyclePolicyText;
    /**
     * The time stamp of the last time that the lifecycle policy was run.
     */
    lastEvaluatedAt?: EvaluationTimestamp;
  }
  export interface GetRegistryPolicyRequest {
  }
  export interface GetRegistryPolicyResponse {
    /**
     * The registry ID associated with the request.
     */
    registryId?: RegistryId;
    /**
     * The JSON text of the permissions policy for a registry.
     */
    policyText?: RegistryPolicyText;
  }
  export interface GetRegistryScanningConfigurationRequest {
  }
  export interface GetRegistryScanningConfigurationResponse {
    /**
     * The registry ID associated with the request.
     */
    registryId?: RegistryId;
    /**
     * The scanning configuration for the registry.
     */
    scanningConfiguration?: RegistryScanningConfiguration;
  }
  export interface GetRepositoryPolicyRequest {
    /**
     * The Amazon Web Services account ID associated with the registry that contains the repository. If you do not specify a registry, the default registry is assumed.
     */
    registryId?: RegistryId;
    /**
     * The name of the repository with the policy to retrieve.
     */
    repositoryName: RepositoryName;
  }
  export interface GetRepositoryPolicyResponse {
    /**
     * The registry ID associated with the request.
     */
    registryId?: RegistryId;
    /**
     * The repository name associated with the request.
     */
    repositoryName?: RepositoryName;
    /**
     * The JSON repository policy text associated with the repository.
     */
    policyText?: RepositoryPolicyText;
  }
  export interface Image {
    /**
     * The Amazon Web Services account ID associated with the registry containing the image.
     */
    registryId?: RegistryId;
    /**
     * The name of the repository associated with the image.
     */
    repositoryName?: RepositoryName;
    /**
     * An object containing the image tag and image digest associated with an image.
     */
    imageId?: ImageIdentifier;
    /**
     * The image manifest associated with the image.
     */
    imageManifest?: ImageManifest;
    /**
     * The manifest media type of the image.
     */
    imageManifestMediaType?: MediaType;
  }
  export type ImageActionType = "EXPIRE"|string;
  export type ImageCount = number;
  export interface ImageDetail {
    /**
     * The Amazon Web Services account ID associated with the registry to which this image belongs.
     */
    registryId?: RegistryId;
    /**
     * The name of the repository to which this image belongs.
     */
    repositoryName?: RepositoryName;
    /**
     * The sha256 digest of the image manifest.
     */
    imageDigest?: ImageDigest;
    /**
     * The list of tags associated with this image.
     */
    imageTags?: ImageTagList;
    /**
     * The size, in bytes, of the image in the repository. If the image is a manifest list, this will be the max size of all manifests in the list.  Beginning with Docker version 1.9, the Docker client compresses image layers before pushing them to a V2 Docker registry. The output of the docker images command shows the uncompressed image size, so it may return a larger image size than the image sizes returned by DescribeImages. 
     */
    imageSizeInBytes?: ImageSizeInBytes;
    /**
     * The date and time, expressed in standard JavaScript date format, at which the current image was pushed to the repository. 
     */
    imagePushedAt?: PushTimestamp;
    /**
     * The current state of the scan.
     */
    imageScanStatus?: ImageScanStatus;
    /**
     * A summary of the last completed image scan.
     */
    imageScanFindingsSummary?: ImageScanFindingsSummary;
    /**
     * The media type of the image manifest.
     */
    imageManifestMediaType?: MediaType;
    /**
     * The artifact media type of the image.
     */
    artifactMediaType?: MediaType;
    /**
     * The date and time, expressed in standard JavaScript date format, when Amazon ECR recorded the last image pull.  Amazon ECR refreshes the last image pull timestamp at least once every 24 hours. For example, if you pull an image once a day then the lastRecordedPullTime timestamp will indicate the exact time that the image was last pulled. However, if you pull an image once an hour, because Amazon ECR refreshes the lastRecordedPullTime timestamp at least once every 24 hours, the result may not be the exact time that the image was last pulled. 
     */
    lastRecordedPullTime?: RecordedPullTimestamp;
  }
  export type ImageDetailList = ImageDetail[];
  export type ImageDigest = string;
  export interface ImageFailure {
    /**
     * The image ID associated with the failure.
     */
    imageId?: ImageIdentifier;
    /**
     * The code associated with the failure.
     */
    failureCode?: ImageFailureCode;
    /**
     * The reason for the failure.
     */
    failureReason?: ImageFailureReason;
  }
  export type ImageFailureCode = "InvalidImageDigest"|"InvalidImageTag"|"ImageTagDoesNotMatchDigest"|"ImageNotFound"|"MissingDigestAndTag"|"ImageReferencedByManifestList"|"KmsError"|"UpstreamAccessDenied"|"UpstreamTooManyRequests"|"UpstreamUnavailable"|string;
  export type ImageFailureList = ImageFailure[];
  export type ImageFailureReason = string;
  export interface ImageIdentifier {
    /**
     * The sha256 digest of the image manifest.
     */
    imageDigest?: ImageDigest;
    /**
     * The tag used for the image.
     */
    imageTag?: ImageTag;
  }
  export type ImageIdentifierList = ImageIdentifier[];
  export type ImageList = Image[];
  export type ImageManifest = string;
  export interface ImageReplicationStatus {
    /**
     * The destination Region for the image replication.
     */
    region?: Region;
    /**
     * The Amazon Web Services account ID associated with the registry to which the image belongs.
     */
    registryId?: RegistryId;
    /**
     * The image replication status.
     */
    status?: ReplicationStatus;
    /**
     * The failure code for a replication that has failed.
     */
    failureCode?: ReplicationError;
  }
  export type ImageReplicationStatusList = ImageReplicationStatus[];
  export interface ImageScanFinding {
    /**
     * The name associated with the finding, usually a CVE number.
     */
    name?: FindingName;
    /**
     * The description of the finding.
     */
    description?: FindingDescription;
    /**
     * A link containing additional details about the security vulnerability.
     */
    uri?: Url;
    /**
     * The finding severity.
     */
    severity?: FindingSeverity;
    /**
     * A collection of attributes of the host from which the finding is generated.
     */
    attributes?: AttributeList;
  }
  export type ImageScanFindingList = ImageScanFinding[];
  export interface ImageScanFindings {
    /**
     * The time of the last completed image scan.
     */
    imageScanCompletedAt?: ScanTimestamp;
    /**
     * The time when the vulnerability data was last scanned.
     */
    vulnerabilitySourceUpdatedAt?: VulnerabilitySourceUpdateTimestamp;
    /**
     * The image vulnerability counts, sorted by severity.
     */
    findingSeverityCounts?: FindingSeverityCounts;
    /**
     * The findings from the image scan.
     */
    findings?: ImageScanFindingList;
    /**
     * Details about the enhanced scan findings from Amazon Inspector.
     */
    enhancedFindings?: EnhancedImageScanFindingList;
  }
  export interface ImageScanFindingsSummary {
    /**
     * The time of the last completed image scan.
     */
    imageScanCompletedAt?: ScanTimestamp;
    /**
     * The time when the vulnerability data was last scanned.
     */
    vulnerabilitySourceUpdatedAt?: VulnerabilitySourceUpdateTimestamp;
    /**
     * The image vulnerability counts, sorted by severity.
     */
    findingSeverityCounts?: FindingSeverityCounts;
  }
  export interface ImageScanStatus {
    /**
     * The current state of an image scan.
     */
    status?: ScanStatus;
    /**
     * The description of the image scan status.
     */
    description?: ScanStatusDescription;
  }
  export interface ImageScanningConfiguration {
    /**
     * The setting that determines whether images are scanned after being pushed to a repository. If set to true, images will be scanned after being pushed. If this parameter is not specified, it will default to false and images will not be scanned unless a scan is manually started with the API_StartImageScan API.
     */
    scanOnPush?: ScanOnPushFlag;
  }
  export type ImageSizeInBytes = number;
  export type ImageTag = string;
  export type ImageTagList = ImageTag[];
  export type ImageTagMutability = "MUTABLE"|"IMMUTABLE"|string;
  export type ImageTagsList = ImageTag[];
  export interface InitiateLayerUploadRequest {
    /**
     * The Amazon Web Services account ID associated with the registry to which you intend to upload layers. If you do not specify a registry, the default registry is assumed.
     */
    registryId?: RegistryId;
    /**
     * The name of the repository to which you intend to upload layers.
     */
    repositoryName: RepositoryName;
  }
  export interface InitiateLayerUploadResponse {
    /**
     * The upload ID for the layer upload. This parameter is passed to further UploadLayerPart and CompleteLayerUpload operations.
     */
    uploadId?: UploadId;
    /**
     * The size, in bytes, that Amazon ECR expects future layer part uploads to be.
     */
    partSize?: PartSize;
  }
  export type IsPTCRuleValid = boolean;
  export type KmsKey = string;
  export type KmsKeyForRepositoryCreationTemplate = string;
  export interface Layer {
    /**
     * The sha256 digest of the image layer.
     */
    layerDigest?: LayerDigest;
    /**
     * The availability status of the image layer.
     */
    layerAvailability?: LayerAvailability;
    /**
     * The size, in bytes, of the image layer.
     */
    layerSize?: LayerSizeInBytes;
    /**
     * The media type of the layer, such as application/vnd.docker.image.rootfs.diff.tar.gzip or application/vnd.oci.image.layer.v1.tar+gzip.
     */
    mediaType?: MediaType;
  }
  export type LayerAvailability = "AVAILABLE"|"UNAVAILABLE"|string;
  export type LayerDigest = string;
  export type LayerDigestList = LayerDigest[];
  export interface LayerFailure {
    /**
     * The layer digest associated with the failure.
     */
    layerDigest?: BatchedOperationLayerDigest;
    /**
     * The failure code associated with the failure.
     */
    failureCode?: LayerFailureCode;
    /**
     * The reason for the failure.
     */
    failureReason?: LayerFailureReason;
  }
  export type LayerFailureCode = "InvalidLayerDigest"|"MissingLayerDigest"|string;
  export type LayerFailureList = LayerFailure[];
  export type LayerFailureReason = string;
  export type LayerList = Layer[];
  export type LayerPartBlob = Buffer|Uint8Array|Blob|string;
  export type LayerSizeInBytes = number;
  export interface LifecyclePolicyPreviewFilter {
    /**
     * The tag status of the image.
     */
    tagStatus?: TagStatus;
  }
  export interface LifecyclePolicyPreviewResult {
    /**
     * The list of tags associated with this image.
     */
    imageTags?: ImageTagList;
    /**
     * The sha256 digest of the image manifest.
     */
    imageDigest?: ImageDigest;
    /**
     * The date and time, expressed in standard JavaScript date format, at which the current image was pushed to the repository.
     */
    imagePushedAt?: PushTimestamp;
    /**
     * The type of action to be taken.
     */
    action?: LifecyclePolicyRuleAction;
    /**
     * The priority of the applied rule.
     */
    appliedRulePriority?: LifecyclePolicyRulePriority;
  }
  export type LifecyclePolicyPreviewResultList = LifecyclePolicyPreviewResult[];
  export type LifecyclePolicyPreviewStatus = "IN_PROGRESS"|"COMPLETE"|"EXPIRED"|"FAILED"|string;
  export interface LifecyclePolicyPreviewSummary {
    /**
     * The number of expiring images.
     */
    expiringImageTotalCount?: ImageCount;
  }
  export interface LifecyclePolicyRuleAction {
    /**
     * The type of action to be taken.
     */
    type?: ImageActionType;
  }
  export type LifecyclePolicyRulePriority = number;
  export type LifecyclePolicyText = string;
  export type LifecyclePolicyTextForRepositoryCreationTemplate = string;
  export type LifecyclePreviewMaxResults = number;
  export interface ListImagesFilter {
    /**
     * The tag status with which to filter your ListImages results. You can filter results based on whether they are TAGGED or UNTAGGED.
     */
    tagStatus?: TagStatus;
  }
  export interface ListImagesRequest {
    /**
     * The Amazon Web Services account ID associated with the registry that contains the repository in which to list images. If you do not specify a registry, the default registry is assumed.
     */
    registryId?: RegistryId;
    /**
     * The repository with image IDs to be listed.
     */
    repositoryName: RepositoryName;
    /**
     * The nextToken value returned from a previous paginated ListImages request where maxResults was used and the results exceeded the value of that parameter. Pagination continues from the end of the previous results that returned the nextToken value. This value is null when there are no more results to return.  This token should be treated as an opaque identifier that is only used to retrieve the next items in a list and not for other programmatic purposes. 
     */
    nextToken?: NextToken;
    /**
     * The maximum number of image results returned by ListImages in paginated output. When this parameter is used, ListImages only returns maxResults results in a single page along with a nextToken response element. The remaining results of the initial request can be seen by sending another ListImages request with the returned nextToken value. This value can be between 1 and 1000. If this parameter is not used, then ListImages returns up to 100 results and a nextToken value, if applicable.
     */
    maxResults?: MaxResults;
    /**
     * The filter key and value with which to filter your ListImages results.
     */
    filter?: ListImagesFilter;
  }
  export interface ListImagesResponse {
    /**
     * The list of image IDs for the requested repository.
     */
    imageIds?: ImageIdentifierList;
    /**
     * The nextToken value to include in a future ListImages request. When the results of a ListImages request exceed maxResults, this value can be used to retrieve the next page of results. This value is null when there are no more results to return.
     */
    nextToken?: NextToken;
  }
  export interface ListTagsForResourceRequest {
    /**
     * The Amazon Resource Name (ARN) that identifies the resource for which to list the tags. Currently, the only supported resource is an Amazon ECR repository.
     */
    resourceArn: Arn;
  }
  export interface ListTagsForResourceResponse {
    /**
     * The tags for the resource.
     */
    tags?: TagList;
  }
  export type MaxResults = number;
  export type MediaType = string;
  export type MediaTypeList = MediaType[];
  export type Metric = string;
  export type NextToken = string;
  export type PTCValidateFailure = string;
  export type PackageManager = string;
  export interface PackageVulnerabilityDetails {
    /**
     * An object that contains details about the CVSS score of a finding.
     */
    cvss?: CvssScoreList;
    /**
     * One or more URLs that contain details about this vulnerability type.
     */
    referenceUrls?: ReferenceUrlsList;
    /**
     * One or more vulnerabilities related to the one identified in this finding.
     */
    relatedVulnerabilities?: RelatedVulnerabilitiesList;
    /**
     * The source of the vulnerability information.
     */
    source?: Source;
    /**
     * A URL to the source of the vulnerability information.
     */
    sourceUrl?: Url;
    /**
     * The date and time that this vulnerability was first added to the vendor's database.
     */
    vendorCreatedAt?: _Date;
    /**
     * The severity the vendor has given to this vulnerability type.
     */
    vendorSeverity?: Severity;
    /**
     * The date and time the vendor last updated this vulnerability in their database.
     */
    vendorUpdatedAt?: _Date;
    /**
     * The ID given to this vulnerability.
     */
    vulnerabilityId?: VulnerabilityId;
    /**
     * The packages impacted by this vulnerability.
     */
    vulnerablePackages?: VulnerablePackagesList;
  }
  export type PartSize = number;
  export type Platform = string;
  export type Prefix = string;
  export type PrefixList = Prefix[];
  export type ProxyEndpoint = string;
  export interface PullThroughCacheRule {
    /**
     * The Amazon ECR repository prefix associated with the pull through cache rule.
     */
    ecrRepositoryPrefix?: PullThroughCacheRuleRepositoryPrefix;
    /**
     * The upstream registry URL associated with the pull through cache rule.
     */
    upstreamRegistryUrl?: Url;
    /**
     * The date and time the pull through cache was created.
     */
    createdAt?: CreationTimestamp;
    /**
     * The Amazon Web Services account ID associated with the registry the pull through cache rule is associated with.
     */
    registryId?: RegistryId;
    /**
     * The ARN of the Secrets Manager secret associated with the pull through cache rule.
     */
    credentialArn?: CredentialArn;
    /**
     * The name of the upstream source registry associated with the pull through cache rule.
     */
    upstreamRegistry?: UpstreamRegistry;
    /**
     * The date and time, in JavaScript date format, when the pull through cache rule was last updated.
     */
    updatedAt?: UpdatedTimestamp;
  }
  export type PullThroughCacheRuleList = PullThroughCacheRule[];
  export type PullThroughCacheRuleRepositoryPrefix = string;
  export type PullThroughCacheRuleRepositoryPrefixList = PullThroughCacheRuleRepositoryPrefix[];
  export type PushTimestamp = Date;
  export interface PutAccountSettingRequest {
    /**
     * Basic scan type version name. 
     */
    name: AccountSettingName;
    /**
     * Setting value that determines what basic scan type is being used: AWS_NATIVE or CLAIR.
     */
    value: AccountSettingValue;
  }
  export interface PutAccountSettingResponse {
    /**
     * Retrieves the the basic scan type version name.
     */
    name?: AccountSettingName;
    /**
     * Retrieves the basic scan type value, either AWS_NATIVE or -.
     */
    value?: AccountSettingValue;
  }
  export interface PutImageRequest {
    /**
     * The Amazon Web Services account ID associated with the registry that contains the repository in which to put the image. If you do not specify a registry, the default registry is assumed.
     */
    registryId?: RegistryId;
    /**
     * The name of the repository in which to put the image.
     */
    repositoryName: RepositoryName;
    /**
     * The image manifest corresponding to the image to be uploaded.
     */
    imageManifest: ImageManifest;
    /**
     * The media type of the image manifest. If you push an image manifest that does not contain the mediaType field, you must specify the imageManifestMediaType in the request.
     */
    imageManifestMediaType?: MediaType;
    /**
     * The tag to associate with the image. This parameter is required for images that use the Docker Image Manifest V2 Schema 2 or Open Container Initiative (OCI) formats.
     */
    imageTag?: ImageTag;
    /**
     * The image digest of the image manifest corresponding to the image.
     */
    imageDigest?: ImageDigest;
  }
  export interface PutImageResponse {
    /**
     * Details of the image uploaded.
     */
    image?: Image;
  }
  export interface PutImageScanningConfigurationRequest {
    /**
     * The Amazon Web Services account ID associated with the registry that contains the repository in which to update the image scanning configuration setting. If you do not specify a registry, the default registry is assumed.
     */
    registryId?: RegistryId;
    /**
     * The name of the repository in which to update the image scanning configuration setting.
     */
    repositoryName: RepositoryName;
    /**
     * The image scanning configuration for the repository. This setting determines whether images are scanned for known vulnerabilities after being pushed to the repository.
     */
    imageScanningConfiguration: ImageScanningConfiguration;
  }
  export interface PutImageScanningConfigurationResponse {
    /**
     * The registry ID associated with the request.
     */
    registryId?: RegistryId;
    /**
     * The repository name associated with the request.
     */
    repositoryName?: RepositoryName;
    /**
     * The image scanning configuration setting for the repository.
     */
    imageScanningConfiguration?: ImageScanningConfiguration;
  }
  export interface PutImageTagMutabilityRequest {
    /**
     * The Amazon Web Services account ID associated with the registry that contains the repository in which to update the image tag mutability settings. If you do not specify a registry, the default registry is assumed.
     */
    registryId?: RegistryId;
    /**
     * The name of the repository in which to update the image tag mutability settings.
     */
    repositoryName: RepositoryName;
    /**
     * The tag mutability setting for the repository. If MUTABLE is specified, image tags can be overwritten. If IMMUTABLE is specified, all image tags within the repository will be immutable which will prevent them from being overwritten.
     */
    imageTagMutability: ImageTagMutability;
  }
  export interface PutImageTagMutabilityResponse {
    /**
     * The registry ID associated with the request.
     */
    registryId?: RegistryId;
    /**
     * The repository name associated with the request.
     */
    repositoryName?: RepositoryName;
    /**
     * The image tag mutability setting for the repository.
     */
    imageTagMutability?: ImageTagMutability;
  }
  export interface PutLifecyclePolicyRequest {
    /**
     * The Amazon Web Services account ID associated with the registry that contains the repository. If you do&#x2028; not specify a registry, the default registry is assumed.
     */
    registryId?: RegistryId;
    /**
     * The name of the repository to receive the policy.
     */
    repositoryName: RepositoryName;
    /**
     * The JSON repository policy text to apply to the repository.
     */
    lifecyclePolicyText: LifecyclePolicyText;
  }
  export interface PutLifecyclePolicyResponse {
    /**
     * The registry ID associated with the request.
     */
    registryId?: RegistryId;
    /**
     * The repository name associated with the request.
     */
    repositoryName?: RepositoryName;
    /**
     * The JSON repository policy text.
     */
    lifecyclePolicyText?: LifecyclePolicyText;
  }
  export interface PutRegistryPolicyRequest {
    /**
     * The JSON policy text to apply to your registry. The policy text follows the same format as IAM policy text. For more information, see Registry permissions in the Amazon Elastic Container Registry User Guide.
     */
    policyText: RegistryPolicyText;
  }
  export interface PutRegistryPolicyResponse {
    /**
     * The registry ID associated with the request.
     */
    registryId?: RegistryId;
    /**
     * The JSON policy text for your registry.
     */
    policyText?: RegistryPolicyText;
  }
  export interface PutRegistryScanningConfigurationRequest {
    /**
     * The scanning type to set for the registry. When a registry scanning configuration is not defined, by default the BASIC scan type is used. When basic scanning is used, you may specify filters to determine which individual repositories, or all repositories, are scanned when new images are pushed to those repositories. Alternatively, you can do manual scans of images with basic scanning. When the ENHANCED scan type is set, Amazon Inspector provides automated vulnerability scanning. You may choose between continuous scanning or scan on push and you may specify filters to determine which individual repositories, or all repositories, are scanned.
     */
    scanType?: ScanType;
    /**
     * The scanning rules to use for the registry. A scanning rule is used to determine which repository filters are used and at what frequency scanning will occur.
     */
    rules?: RegistryScanningRuleList;
  }
  export interface PutRegistryScanningConfigurationResponse {
    /**
     * The scanning configuration for your registry.
     */
    registryScanningConfiguration?: RegistryScanningConfiguration;
  }
  export interface PutReplicationConfigurationRequest {
    /**
     * An object representing the replication configuration for a registry.
     */
    replicationConfiguration: ReplicationConfiguration;
  }
  export interface PutReplicationConfigurationResponse {
    /**
     * The contents of the replication configuration for the registry.
     */
    replicationConfiguration?: ReplicationConfiguration;
  }
  export type RCTAppliedFor = "REPLICATION"|"PULL_THROUGH_CACHE"|string;
  export type RCTAppliedForList = RCTAppliedFor[];
  export type Reason = string;
  export interface Recommendation {
    /**
     * The URL address to the CVE remediation recommendations.
     */
    url?: Url;
    /**
     * The recommended course of action to remediate the finding.
     */
    text?: RecommendationText;
  }
  export type RecommendationText = string;
  export type RecordedPullTimestamp = Date;
  export type ReferenceUrlsList = Url[];
  export type Region = string;
  export type RegistryId = string;
  export type RegistryPolicyText = string;
  export interface RegistryScanningConfiguration {
    /**
     * The type of scanning configured for the registry.
     */
    scanType?: ScanType;
    /**
     * The scanning rules associated with the registry.
     */
    rules?: RegistryScanningRuleList;
  }
  export interface RegistryScanningRule {
    /**
     * The frequency that scans are performed at for a private registry. When the ENHANCED scan type is specified, the supported scan frequencies are CONTINUOUS_SCAN and SCAN_ON_PUSH. When the BASIC scan type is specified, the SCAN_ON_PUSH scan frequency is supported. If scan on push is not specified, then the MANUAL scan frequency is set by default.
     */
    scanFrequency: ScanFrequency;
    /**
     * The repository filters associated with the scanning configuration for a private registry.
     */
    repositoryFilters: ScanningRepositoryFilterList;
  }
  export type RegistryScanningRuleList = RegistryScanningRule[];
  export type RelatedVulnerabilitiesList = RelatedVulnerability[];
  export type RelatedVulnerability = string;
  export type Release = string;
  export interface Remediation {
    /**
     * An object that contains information about the recommended course of action to remediate the finding.
     */
    recommendation?: Recommendation;
  }
  export interface ReplicationConfiguration {
    /**
     * An array of objects representing the replication destinations and repository filters for a replication configuration.
     */
    rules: ReplicationRuleList;
  }
  export interface ReplicationDestination {
    /**
     * The Region to replicate to.
     */
    region: Region;
    /**
     * The Amazon Web Services account ID of the Amazon ECR private registry to replicate to. When configuring cross-Region replication within your own registry, specify your own account ID.
     */
    registryId: RegistryId;
  }
  export type ReplicationDestinationList = ReplicationDestination[];
  export type ReplicationError = string;
  export interface ReplicationRule {
    /**
     * An array of objects representing the destination for a replication rule.
     */
    destinations: ReplicationDestinationList;
    /**
     * An array of objects representing the filters for a replication rule. Specifying a repository filter for a replication rule provides a method for controlling which repositories in a private registry are replicated.
     */
    repositoryFilters?: RepositoryFilterList;
  }
  export type ReplicationRuleList = ReplicationRule[];
  export type ReplicationStatus = "IN_PROGRESS"|"COMPLETE"|"FAILED"|string;
  export interface Repository {
    /**
     * The Amazon Resource Name (ARN) that identifies the repository. The ARN contains the arn:aws:ecr namespace, followed by the region of the repository, Amazon Web Services account ID of the repository owner, repository namespace, and repository name. For example, arn:aws:ecr:region:************:repository-namespace/repository-name.
     */
    repositoryArn?: Arn;
    /**
     * The Amazon Web Services account ID associated with the registry that contains the repository.
     */
    registryId?: RegistryId;
    /**
     * The name of the repository.
     */
    repositoryName?: RepositoryName;
    /**
     * The URI for the repository. You can use this URI for container image push and pull operations.
     */
    repositoryUri?: Url;
    /**
     * The date and time, in JavaScript date format, when the repository was created.
     */
    createdAt?: CreationTimestamp;
    /**
     * The tag mutability setting for the repository.
     */
    imageTagMutability?: ImageTagMutability;
    imageScanningConfiguration?: ImageScanningConfiguration;
    /**
     * The encryption configuration for the repository. This determines how the contents of your repository are encrypted at rest.
     */
    encryptionConfiguration?: EncryptionConfiguration;
  }
  export interface RepositoryCreationTemplate {
    /**
     * The repository namespace prefix associated with the repository creation template.
     */
    prefix?: Prefix;
    /**
     * The description associated with the repository creation template.
     */
    description?: RepositoryTemplateDescription;
    /**
     * The encryption configuration associated with the repository creation template.
     */
    encryptionConfiguration?: EncryptionConfigurationForRepositoryCreationTemplate;
    /**
     * The metadata to apply to the repository to help you categorize and organize. Each tag consists of a key and an optional value, both of which you define. Tag keys can have a maximum character length of 128 characters, and tag values can have a maximum length of 256 characters.
     */
    resourceTags?: TagList;
    /**
     * The tag mutability setting for the repository. If this parameter is omitted, the default setting of MUTABLE will be used which will allow image tags to be overwritten. If IMMUTABLE is specified, all image tags within the repository will be immutable which will prevent them from being overwritten.
     */
    imageTagMutability?: ImageTagMutability;
    /**
     * he repository policy to apply to repositories created using the template. A repository policy is a permissions policy associated with a repository to control access permissions. 
     */
    repositoryPolicy?: RepositoryPolicyText;
    /**
     * The lifecycle policy to use for repositories created using the template.
     */
    lifecyclePolicy?: LifecyclePolicyTextForRepositoryCreationTemplate;
    /**
     * A list of enumerable Strings representing the repository creation scenarios that this template will apply towards. The two supported scenarios are PULL_THROUGH_CACHE and REPLICATION
     */
    appliedFor?: RCTAppliedForList;
    /**
     * The ARN of the role to be assumed by Amazon ECR. Amazon ECR will assume your supplied role when the customRoleArn is specified. When this field isn't specified, Amazon ECR will use the service-linked role for the repository creation template.
     */
    customRoleArn?: CustomRoleArn;
    /**
     * The date and time, in JavaScript date format, when the repository creation template was created.
     */
    createdAt?: _Date;
    /**
     * The date and time, in JavaScript date format, when the repository creation template was last updated.
     */
    updatedAt?: _Date;
  }
  export type RepositoryCreationTemplateList = RepositoryCreationTemplate[];
  export interface RepositoryFilter {
    /**
     * The repository filter details. When the PREFIX_MATCH filter type is specified, this value is required and should be the repository name prefix to configure replication for.
     */
    filter: RepositoryFilterValue;
    /**
     * The repository filter type. The only supported value is PREFIX_MATCH, which is a repository name prefix specified with the filter parameter.
     */
    filterType: RepositoryFilterType;
  }
  export type RepositoryFilterList = RepositoryFilter[];
  export type RepositoryFilterType = "PREFIX_MATCH"|string;
  export type RepositoryFilterValue = string;
  export type RepositoryList = Repository[];
  export type RepositoryName = string;
  export type RepositoryNameList = RepositoryName[];
  export type RepositoryPolicyText = string;
  export interface RepositoryScanningConfiguration {
    /**
     * The ARN of the repository.
     */
    repositoryArn?: Arn;
    /**
     * The name of the repository.
     */
    repositoryName?: RepositoryName;
    /**
     * Whether or not scan on push is configured for the repository.
     */
    scanOnPush?: ScanOnPushFlag;
    /**
     * The scan frequency for the repository.
     */
    scanFrequency?: ScanFrequency;
    /**
     * The scan filters applied to the repository.
     */
    appliedScanFilters?: ScanningRepositoryFilterList;
  }
  export interface RepositoryScanningConfigurationFailure {
    /**
     * The name of the repository.
     */
    repositoryName?: RepositoryName;
    /**
     * The failure code.
     */
    failureCode?: ScanningConfigurationFailureCode;
    /**
     * The reason for the failure.
     */
    failureReason?: ScanningConfigurationFailureReason;
  }
  export type RepositoryScanningConfigurationFailureList = RepositoryScanningConfigurationFailure[];
  export type RepositoryScanningConfigurationList = RepositoryScanningConfiguration[];
  export type RepositoryTemplateDescription = string;
  export interface Resource {
    /**
     * An object that contains details about the resource involved in a finding.
     */
    details?: ResourceDetails;
    /**
     * The ID of the resource.
     */
    id?: ResourceId;
    /**
     * The tags attached to the resource.
     */
    tags?: Tags;
    /**
     * The type of resource.
     */
    type?: Type;
  }
  export interface ResourceDetails {
    /**
     * An object that contains details about the Amazon ECR container image involved in the finding.
     */
    awsEcrContainerImage?: AwsEcrContainerImageDetails;
  }
  export type ResourceId = string;
  export type ResourceList = Resource[];
  export type ScanFrequency = "SCAN_ON_PUSH"|"CONTINUOUS_SCAN"|"MANUAL"|string;
  export type ScanOnPushFlag = boolean;
  export type ScanStatus = "IN_PROGRESS"|"COMPLETE"|"FAILED"|"UNSUPPORTED_IMAGE"|"ACTIVE"|"PENDING"|"SCAN_ELIGIBILITY_EXPIRED"|"FINDINGS_UNAVAILABLE"|string;
  export type ScanStatusDescription = string;
  export type ScanTimestamp = Date;
  export type ScanType = "BASIC"|"ENHANCED"|string;
  export type ScanningConfigurationFailureCode = "REPOSITORY_NOT_FOUND"|string;
  export type ScanningConfigurationFailureReason = string;
  export type ScanningConfigurationRepositoryNameList = RepositoryName[];
  export interface ScanningRepositoryFilter {
    /**
     * The filter to use when scanning.
     */
    filter: ScanningRepositoryFilterValue;
    /**
     * The type associated with the filter.
     */
    filterType: ScanningRepositoryFilterType;
  }
  export type ScanningRepositoryFilterList = ScanningRepositoryFilter[];
  export type ScanningRepositoryFilterType = "WILDCARD"|string;
  export type ScanningRepositoryFilterValue = string;
  export type Score = number;
  export interface ScoreDetails {
    /**
     * An object that contains details about the CVSS score given to a finding.
     */
    cvss?: CvssScoreDetails;
  }
  export type ScoringVector = string;
  export interface SetRepositoryPolicyRequest {
    /**
     * The Amazon Web Services account ID associated with the registry that contains the repository. If you do not specify a registry, the default registry is assumed.
     */
    registryId?: RegistryId;
    /**
     * The name of the repository to receive the policy.
     */
    repositoryName: RepositoryName;
    /**
     * The JSON repository policy text to apply to the repository. For more information, see Amazon ECR repository policies in the Amazon Elastic Container Registry User Guide.
     */
    policyText: RepositoryPolicyText;
    /**
     * If the policy you are attempting to set on a repository policy would prevent you from setting another policy in the future, you must force the SetRepositoryPolicy operation. This is intended to prevent accidental repository lock outs.
     */
    force?: ForceFlag;
  }
  export interface SetRepositoryPolicyResponse {
    /**
     * The registry ID associated with the request.
     */
    registryId?: RegistryId;
    /**
     * The repository name associated with the request.
     */
    repositoryName?: RepositoryName;
    /**
     * The JSON repository policy text applied to the repository.
     */
    policyText?: RepositoryPolicyText;
  }
  export type Severity = string;
  export type SeverityCount = number;
  export type Source = string;
  export type SourceLayerHash = string;
  export interface StartImageScanRequest {
    /**
     * The Amazon Web Services account ID associated with the registry that contains the repository in which to start an image scan request. If you do not specify a registry, the default registry is assumed.
     */
    registryId?: RegistryId;
    /**
     * The name of the repository that contains the images to scan.
     */
    repositoryName: RepositoryName;
    imageId: ImageIdentifier;
  }
  export interface StartImageScanResponse {
    /**
     * The registry ID associated with the request.
     */
    registryId?: RegistryId;
    /**
     * The repository name associated with the request.
     */
    repositoryName?: RepositoryName;
    imageId?: ImageIdentifier;
    /**
     * The current state of the scan.
     */
    imageScanStatus?: ImageScanStatus;
  }
  export interface StartLifecyclePolicyPreviewRequest {
    /**
     * The Amazon Web Services account ID associated with the registry that contains the repository. If you do not specify a registry, the default registry is assumed.
     */
    registryId?: RegistryId;
    /**
     * The name of the repository to be evaluated.
     */
    repositoryName: RepositoryName;
    /**
     * The policy to be evaluated against. If you do not specify a policy, the current policy for the repository is used.
     */
    lifecyclePolicyText?: LifecyclePolicyText;
  }
  export interface StartLifecyclePolicyPreviewResponse {
    /**
     * The registry ID associated with the request.
     */
    registryId?: RegistryId;
    /**
     * The repository name associated with the request.
     */
    repositoryName?: RepositoryName;
    /**
     * The JSON repository policy text.
     */
    lifecyclePolicyText?: LifecyclePolicyText;
    /**
     * The status of the lifecycle policy preview request.
     */
    status?: LifecyclePolicyPreviewStatus;
  }
  export type Status = string;
  export interface Tag {
    /**
     * One part of a key-value pair that make up a tag. A key is a general label that acts like a category for more specific tag values.
     */
    Key: TagKey;
    /**
     * A value acts as a descriptor within a tag category (key).
     */
    Value: TagValue;
  }
  export type TagKey = string;
  export type TagKeyList = TagKey[];
  export type TagList = Tag[];
  export interface TagResourceRequest {
    /**
     * The Amazon Resource Name (ARN) of the the resource to which to add tags. Currently, the only supported resource is an Amazon ECR repository.
     */
    resourceArn: Arn;
    /**
     * The tags to add to the resource. A tag is an array of key-value pairs. Tag keys can have a maximum character length of 128 characters, and tag values can have a maximum length of 256 characters.
     */
    tags: TagList;
  }
  export interface TagResourceResponse {
  }
  export type TagStatus = "TAGGED"|"UNTAGGED"|"ANY"|string;
  export type TagValue = string;
  export type Tags = {[key: string]: TagValue};
  export type Title = string;
  export type Type = string;
  export interface UntagResourceRequest {
    /**
     * The Amazon Resource Name (ARN) of the resource from which to remove tags. Currently, the only supported resource is an Amazon ECR repository.
     */
    resourceArn: Arn;
    /**
     * The keys of the tags to be removed.
     */
    tagKeys: TagKeyList;
  }
  export interface UntagResourceResponse {
  }
  export interface UpdatePullThroughCacheRuleRequest {
    /**
     * The Amazon Web Services account ID associated with the registry associated with the pull through cache rule. If you do not specify a registry, the default registry is assumed.
     */
    registryId?: RegistryId;
    /**
     * The repository name prefix to use when caching images from the source registry.
     */
    ecrRepositoryPrefix: PullThroughCacheRuleRepositoryPrefix;
    /**
     * The Amazon Resource Name (ARN) of the Amazon Web Services Secrets Manager secret that identifies the credentials to authenticate to the upstream registry.
     */
    credentialArn: CredentialArn;
  }
  export interface UpdatePullThroughCacheRuleResponse {
    /**
     * The Amazon ECR repository prefix associated with the pull through cache rule.
     */
    ecrRepositoryPrefix?: PullThroughCacheRuleRepositoryPrefix;
    /**
     * The registry ID associated with the request.
     */
    registryId?: RegistryId;
    /**
     * The date and time, in JavaScript date format, when the pull through cache rule was updated.
     */
    updatedAt?: UpdatedTimestamp;
    /**
     * The Amazon Resource Name (ARN) of the Amazon Web Services Secrets Manager secret associated with the pull through cache rule.
     */
    credentialArn?: CredentialArn;
  }
  export interface UpdateRepositoryCreationTemplateRequest {
    /**
     * The repository namespace prefix that matches an existing repository creation template in the registry. All repositories created using this namespace prefix will have the settings defined in this template applied. For example, a prefix of prod would apply to all repositories beginning with prod/. This includes a repository named prod/team1 as well as a repository named prod/repository1. To apply a template to all repositories in your registry that don't have an associated creation template, you can use ROOT as the prefix.
     */
    prefix: Prefix;
    /**
     * A description for the repository creation template.
     */
    description?: RepositoryTemplateDescription;
    encryptionConfiguration?: EncryptionConfigurationForRepositoryCreationTemplate;
    /**
     * The metadata to apply to the repository to help you categorize and organize. Each tag consists of a key and an optional value, both of which you define. Tag keys can have a maximum character length of 128 characters, and tag values can have a maximum length of 256 characters.
     */
    resourceTags?: TagList;
    /**
     * Updates the tag mutability setting for the repository. If this parameter is omitted, the default setting of MUTABLE will be used which will allow image tags to be overwritten. If IMMUTABLE is specified, all image tags within the repository will be immutable which will prevent them from being overwritten.
     */
    imageTagMutability?: ImageTagMutability;
    /**
     * Updates the repository policy created using the template. A repository policy is a permissions policy associated with a repository to control access permissions. 
     */
    repositoryPolicy?: RepositoryPolicyText;
    /**
     * Updates the lifecycle policy associated with the specified repository creation template.
     */
    lifecyclePolicy?: LifecyclePolicyTextForRepositoryCreationTemplate;
    /**
     * Updates the list of enumerable strings representing the Amazon ECR repository creation scenarios that this template will apply towards. The two supported scenarios are PULL_THROUGH_CACHE and REPLICATION 
     */
    appliedFor?: RCTAppliedForList;
    /**
     * The ARN of the role to be assumed by Amazon ECR. This role must be in the same account as the registry that you are configuring. Amazon ECR will assume your supplied role when the customRoleArn is specified. When this field isn't specified, Amazon ECR will use the service-linked role for the repository creation template.
     */
    customRoleArn?: CustomRoleArn;
  }
  export interface UpdateRepositoryCreationTemplateResponse {
    /**
     * The registry ID associated with the request.
     */
    registryId?: RegistryId;
    /**
     * The details of the repository creation template associated with the request.
     */
    repositoryCreationTemplate?: RepositoryCreationTemplate;
  }
  export type UpdatedTimestamp = Date;
  export type UploadId = string;
  export interface UploadLayerPartRequest {
    /**
     * The Amazon Web Services account ID associated with the registry to which you are uploading layer parts. If you do not specify a registry, the default registry is assumed.
     */
    registryId?: RegistryId;
    /**
     * The name of the repository to which you are uploading layer parts.
     */
    repositoryName: RepositoryName;
    /**
     * The upload ID from a previous InitiateLayerUpload operation to associate with the layer part upload.
     */
    uploadId: UploadId;
    /**
     * The position of the first byte of the layer part witin the overall image layer.
     */
    partFirstByte: PartSize;
    /**
     * The position of the last byte of the layer part within the overall image layer.
     */
    partLastByte: PartSize;
    /**
     * The base64-encoded layer part payload.
     */
    layerPartBlob: LayerPartBlob;
  }
  export interface UploadLayerPartResponse {
    /**
     * The registry ID associated with the request.
     */
    registryId?: RegistryId;
    /**
     * The repository name associated with the request.
     */
    repositoryName?: RepositoryName;
    /**
     * The upload ID associated with the request.
     */
    uploadId?: UploadId;
    /**
     * The integer value of the last byte received in the request.
     */
    lastByteReceived?: PartSize;
  }
  export type UpstreamRegistry = "ecr-public"|"quay"|"k8s"|"docker-hub"|"github-container-registry"|"azure-container-registry"|"gitlab-container-registry"|string;
  export type Url = string;
  export interface ValidatePullThroughCacheRuleRequest {
    /**
     * The repository name prefix associated with the pull through cache rule.
     */
    ecrRepositoryPrefix: PullThroughCacheRuleRepositoryPrefix;
    /**
     * The registry ID associated with the pull through cache rule. If you do not specify a registry, the default registry is assumed.
     */
    registryId?: RegistryId;
  }
  export interface ValidatePullThroughCacheRuleResponse {
    /**
     * The Amazon ECR repository prefix associated with the pull through cache rule.
     */
    ecrRepositoryPrefix?: PullThroughCacheRuleRepositoryPrefix;
    /**
     * The registry ID associated with the request.
     */
    registryId?: RegistryId;
    /**
     * The upstream registry URL associated with the pull through cache rule.
     */
    upstreamRegistryUrl?: Url;
    /**
     * The Amazon Resource Name (ARN) of the Amazon Web Services Secrets Manager secret associated with the pull through cache rule.
     */
    credentialArn?: CredentialArn;
    /**
     * Whether or not the pull through cache rule was validated. If true, Amazon ECR was able to reach the upstream registry and authentication was successful. If false, there was an issue and validation failed. The failure reason indicates the cause.
     */
    isValid?: IsPTCRuleValid;
    /**
     * The reason the validation failed. For more details about possible causes and how to address them, see Using pull through cache rules in the Amazon Elastic Container Registry User Guide.
     */
    failure?: PTCValidateFailure;
  }
  export type Version = string;
  export type VulnerabilityId = string;
  export type VulnerabilitySourceUpdateTimestamp = Date;
  export interface VulnerablePackage {
    /**
     * The architecture of the vulnerable package.
     */
    arch?: Arch;
    /**
     * The epoch of the vulnerable package.
     */
    epoch?: Epoch;
    /**
     * The file path of the vulnerable package.
     */
    filePath?: FilePath;
    /**
     * The name of the vulnerable package.
     */
    name?: VulnerablePackageName;
    /**
     * The package manager of the vulnerable package.
     */
    packageManager?: PackageManager;
    /**
     * The release of the vulnerable package.
     */
    release?: Release;
    /**
     * The source layer hash of the vulnerable package.
     */
    sourceLayerHash?: SourceLayerHash;
    /**
     * The version of the vulnerable package.
     */
    version?: Version;
  }
  export type VulnerablePackageName = string;
  export type VulnerablePackagesList = VulnerablePackage[];
  /**
   * A string in YYYY-MM-DD format that represents the latest possible API version that can be used in this service. Specify 'latest' to use the latest possible version.
   */
  export type apiVersion = "2015-09-21"|"latest"|string;
  export interface ClientApiVersions {
    /**
     * A string in YYYY-MM-DD format that represents the latest possible API version that can be used in this service. Specify 'latest' to use the latest possible version.
     */
    apiVersion?: apiVersion;
  }
  export type ClientConfiguration = ServiceConfigurationOptions & ClientApiVersions;
  /**
   * Contains interfaces for use with the ECR client.
   */
  export import Types = ECR;
}
export = ECR;
